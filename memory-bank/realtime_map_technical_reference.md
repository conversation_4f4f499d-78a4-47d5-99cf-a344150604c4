# RealTimeMap.php 技術參考文件

## 🔑 關鍵程式碼片段

### 1. 地圖初始化模組

```javascript
// 初始化不同地圖類型
function initGoogleMap() {
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: {lat: 25.0330, lng: 121.5654},
        mapTypeId: google.maps.MapTypeId.ROADMAP
    });
}

function initGaodeMap() {
    map = new AMap.Map('map', {
        zoom: 12,
        center: [121.5654, 25.0330],
        resizeEnable: true
    });
}

function initBaiduMap() {
    map = new BMap.Map("map");
    map.centerAndZoom(new BMap.Point(121.5654, 25.0330), 12);
    map.addControl(new BMap.NavigationControl());
}

function initOSMMap() {
    map = L.map('map').setView([25.0330, 121.5654], 12);
    <PERSON><PERSON>tile<PERSON>ayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
}
```

### 2. 團隊標記創建

```javascript
function createTeamMarker(team) {
    const color = showAlliance && team.alliance_id ? 
                  (allianceColors[team.alliance_id] || allianceColors.default) : 
                  '#007bff';
    
    const title = `${team.team_name} (${team.user_count}人)`;
    let marker = null;
    
    switch (currentMapType) {
        case 'google':
            marker = new google.maps.Marker({
                position: {lat: parseFloat(team.lat), lng: parseFloat(team.lng)},
                map: map,
                title: title,
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: color,
                    fillOpacity: 0.8,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 12
                },
                label: {
                    text: team.user_count.toString(),
                    color: '#ffffff',
                    fontSize: '12px',
                    fontWeight: 'bold'
                }
            });
            break;
        // ... 其他地圖類型
    }
    
    if (marker) {
        markers.push(marker);
    }
}
```

### 3. 時間軸位置生成

```javascript
function generateTimelinePositions(timestamp) {
    const hourOffset = (timestamp - timelineStart) / (1000 * 3600);
    
    const teams = [
        {
            team_name: '北區小隊',
            user_count: 5,
            lat: 25.0450 + Math.sin(hourOffset * 0.1) * 0.005,
            lng: 121.5200 + Math.cos(hourOffset * 0.1) * 0.005,
            alliance_id: 1
        },
        // ... 其他團隊
    ];
    
    displayTeams(teams);
}
```

### 4. 自動播放控制

```javascript
function timelinePlay() {
    const playBtn = document.getElementById('play-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const slider = document.getElementById('timeline-slider');
    
    playBtn.style.display = 'none';
    pauseBtn.style.display = 'inline-block';
    
    timelineInterval = setInterval(() => {
        let position = parseInt(slider.value);
        position += 2; // 每次增加2%
        
        if (position > 100) {
            position = 0; // 循環播放
        }
        
        slider.value = position;
        updateTimelinePosition(position);
    }, 1000); // 每秒更新
}
```

## 🗺️ 地圖API配置

### Google Maps
```javascript
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBOUt7IUYq6tJHDH6wCQ_e2bupWylrY2kA"></script>
```

### 高德地圖
```javascript
<script src="https://webapi.amap.com/maps?v=1.4.15&key=e9ab2d50c1e6278f9118bc495eb426c0"></script>
```

### 百度地圖
```javascript
<script src="https://api.map.baidu.com/api?v=2.0&ak=39E8A3734E5940c86424fd1cc7cb629c"></script>
```

### OpenStreetMap (Leaflet)
```html
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js"></script>
```

## 🎨 UI 組件樣式

### 時間軸控制面板
```html
<div id="timeline-container" style="display: none; background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ccc;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <strong>時間軸回顧模式</strong>
        <button onclick="toggleTimelineMode()">退出</button>
    </div>
    
    <div style="margin: 10px 0;">
        <input type="range" id="timeline-slider" 
               min="0" max="100" value="100" 
               oninput="updateTimelinePosition(this.value)">
    </div>
    
    <div style="display: flex; align-items: center; gap: 10px;">
        <button onclick="timelinePlay()" id="play-btn">▶ 播放</button>
        <button onclick="timelinePause()" id="pause-btn" style="display: none;">⏸ 暫停</button>
        <div id="timeline-time">選擇時間點</div>
    </div>
</div>
```

## 📊 數據結構

### 團隊數據格式
```javascript
{
    team_name: '北區小隊',
    user_count: 5,
    lat: 25.0450,
    lng: 121.5200,
    alliance_id: 1,
    alliance_name: '聯盟 1',
    last_update: '2025-07-02 09:00'
}
```

### 聯盟顏色映射
```javascript
let allianceColors = {
    1: '#FF4444', // 紅色
    2: '#4444FF', // 藍色
    3: '#44FF44', // 綠色
    4: '#FFAA00', // 橙色
    5: '#AA44FF', // 紫色
    6: '#00AAFF', // 天藍色
    7: '#FF44AA', // 粉紅色
    8: '#AAFF44', // 萊姆綠
    'default': '#888888' // 默認灰色
};
```

## 🔗 URL 參數

- `g_id` - 遊戲ID（必需）
- `map_type` - 地圖類型（可選）
  - `google` - Google Maps（預設）
  - `gaode` - 高德地圖
  - `baidu` - 百度地圖
  - `osm` - OpenStreetMap
- `show_alliance` - 顯示聯盟顏色（可選）
  - `1` - 顯示（預設）
  - `0` - 不顯示

### 範例
```
https://test.pilotrunapp.com/RealTimeMap.php?g_id=649&map_type=google&show_alliance=1
```

## 🛠️ 整合指南

### 在其他頁面添加連結
```html
<a href="RealTimeMap.php?g_id={$g_id}" target="_blank">小隊動態</a>
```

### 在 Smarty 模板中使用
```smarty
{if $gameLevelType.set_trailmap == 1}
    <a href="RealTimeMap.php?g_id={$g_id}" target="_blank" title="小隊動態">小隊動態</a>
{/if}
```

## ⚠️ 注意事項

1. **獨立運行** - 不依賴外部 PHP 檔案
2. **示例數據** - 目前使用客戶端生成的示例數據
3. **瀏覽器相容性** - 需要支援 ES6 的現代瀏覽器
4. **地圖API限制** - 注意各地圖服務的使用限制