/**
 * 修復空白選單問題
 * 在瀏覽器控制台中運行此腳本
 */

(function() {
    console.log('=== 開始修復空白選單問題 ===');
    
    const $mapSelect = $('#wizard_g_map_type');
    
    if ($mapSelect.length === 0) {
        console.error('找不到地圖選單元素');
        return;
    }
    
    console.log('找到選單元素');
    console.log('當前選項數量:', $mapSelect.find('option').length);
    
    // 檢查當前選項
    $mapSelect.find('option').each(function(index) {
        const $option = $(this);
        console.log(`選項 ${index}: 值="${$option.val()}" 文字="${$option.text()}" 可見=${$option.is(':visible')}`);
    });
    
    // 方案 1: 修復現有選項的樣式
    function fixExistingOptions() {
        console.log('嘗試修復現有選項...');
        
        // 修復選單本身
        $mapSelect.css({
            'appearance': 'menulist',
            '-webkit-appearance': 'menulist',
            '-moz-appearance': 'menulist',
            'color': '#333',
            'background-color': '#fff',
            'font-size': '14px',
            'line-height': '1.5',
            'padding': '8px 12px'
        });
        
        // 修復每個選項
        $mapSelect.find('option').each(function() {
            $(this).css({
                'color': '#333',
                'background-color': '#fff',
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'font-size': '14px',
                'padding': '4px 8px'
            });
        });
        
        // 強制重新渲染
        $mapSelect.hide().show();
        
        console.log('已修復現有選項樣式');
    }
    
    // 方案 2: 重新創建選單選項
    function recreateOptions() {
        console.log('重新創建選單選項...');
        
        const options = [
            {value: '', text: '請選擇地圖類型'},
            {value: '1', text: 'Google地圖 (台灣)'},
            {value: '2', text: 'OSM(臺灣)'},
            {value: '3', text: '高德地圖（臺灣）'},
            {value: '4', text: '高德地圖 (中國)'},
            {value: '5', text: 'OSM 地圖(中國)'}
        ];
        
        // 清空現有選項
        $mapSelect.empty();
        
        // 添加新選項
        options.forEach(function(opt) {
            const $option = $('<option>')
                .val(opt.value)
                .text(opt.text)
                .css({
                    'color': '#333',
                    'background-color': '#fff'
                });
            $mapSelect.append($option);
        });
        
        console.log('已重新創建選項');
    }
    
    // 方案 3: 使用原生 DOM 操作
    function useNativeDOM() {
        console.log('使用原生 DOM 操作...');
        
        const selectElement = document.getElementById('wizard_g_map_type');
        if (!selectElement) {
            console.error('找不到原生選單元素');
            return;
        }
        
        // 清空選項
        selectElement.innerHTML = '';
        
        const options = [
            {value: '', text: '請選擇地圖類型'},
            {value: '1', text: 'Google地圖 (台灣)'},
            {value: '2', text: 'OSM(臺灣)'},
            {value: '3', text: '高德地圖（臺灣）'},
            {value: '4', text: '高德地圖 (中國)'},
            {value: '5', text: 'OSM 地圖(中國)'}
        ];
        
        options.forEach(function(opt) {
            const option = document.createElement('option');
            option.value = opt.value;
            option.textContent = opt.text;
            option.style.color = '#333';
            option.style.backgroundColor = '#fff';
            selectElement.appendChild(option);
        });
        
        // 設置選單樣式
        selectElement.style.appearance = 'menulist';
        selectElement.style.webkitAppearance = 'menulist';
        selectElement.style.mozAppearance = 'menulist';
        selectElement.style.color = '#333';
        selectElement.style.backgroundColor = '#fff';
        selectElement.style.fontSize = '14px';
        
        console.log('已使用原生 DOM 重新創建選項');
    }
    
    // 測試函數
    function testSelect() {
        console.log('測試選單功能...');
        
        setTimeout(() => {
            const optionCount = $mapSelect.find('option').length;
            const visibleOptions = $mapSelect.find('option:visible').length;
            
            console.log(`選項總數: ${optionCount}`);
            console.log(`可見選項: ${visibleOptions}`);
            
            if (optionCount > 0) {
                console.log('✓ 選項已創建');
                
                // 測試選擇功能
                const testValue = $mapSelect.find('option[value!=""]').first().val();
                if (testValue) {
                    $mapSelect.val(testValue);
                    console.log(`✓ 測試選擇值: ${testValue}`);
                    console.log(`✓ 當前選中文字: ${$mapSelect.find('option:selected').text()}`);
                }
            } else {
                console.error('✗ 沒有找到選項');
            }
        }, 500);
    }
    
    // 執行修復
    console.log('開始執行修復方案...');
    
    // 首先嘗試修復現有選項
    fixExistingOptions();
    
    // 如果還是沒有可見選項，重新創建
    setTimeout(() => {
        const visibleOptions = $mapSelect.find('option:visible').length;
        if (visibleOptions === 0) {
            console.log('現有選項修復失敗，嘗試重新創建...');
            recreateOptions();
            
            // 如果 jQuery 方式還是失敗，使用原生 DOM
            setTimeout(() => {
                const stillNoOptions = $mapSelect.find('option').length === 0;
                if (stillNoOptions) {
                    console.log('jQuery 方式失敗，使用原生 DOM...');
                    useNativeDOM();
                }
                
                // 最終測試
                testSelect();
            }, 200);
        } else {
            console.log('現有選項修復成功');
            testSelect();
        }
    }, 200);
    
    console.log('=== 修復腳本執行完成 ===');
    
    // 將修復函數暴露到全局
    window.fixEmptySelect = function() {
        fixExistingOptions();
        setTimeout(() => {
            if ($mapSelect.find('option:visible').length === 0) {
                recreateOptions();
                setTimeout(() => {
                    if ($mapSelect.find('option').length === 0) {
                        useNativeDOM();
                    }
                    testSelect();
                }, 200);
            } else {
                testSelect();
            }
        }, 200);
    };
    
    console.log('可以運行 fixEmptySelect() 來重新執行修復');
})();
