# Smarty 緩存清理指南
## Smarty Cache Clearing Guide

**問題**: 編譯後的模板文件出現錯誤  
**位置**: `/data/wwwroot/test.pilotrunapp.com/templates_c/`  
**日期**: 2025-07-15

---

## 🔧 解決步驟

### 1. **完整清理編譯緩存**
```bash
# 在服務器上執行以下命令
cd /data/wwwroot/test.pilotrunapp.com/
rm -rf templates_c/*
chmod 755 templates_c/
```

### 2. **檢查文件權限**
```bash
# 確保 templates_c 目錄有正確權限
chown -R www-data:www-data templates_c/
chmod -R 755 templates_c/
```

### 3. **檢查 Smarty 配置**
確認 `inc/maininit.php` 中的 Smarty 設定：
```php
$smarty->compile_dir = 'templates_c/';
$smarty->cache_dir = 'cache/';
$smarty->compile_check = true; // 開發環境設為 true
```

### 4. **強制重新編譯**
```php
// 在 PHP 代碼中臨時添加
$smarty->force_compile = true;
$smarty->clearAllCache();
$smarty->clearCompiledTemplate();
```

---

## 🚨 常見問題與解決方案

### **問題 1: 權限不足**
**錯誤**: "Permission denied to write to templates_c"
**解決**:
```bash
sudo chown -R apache:apache /data/wwwroot/test.pilotrunapp.com/templates_c/
sudo chmod -R 755 /data/wwwroot/test.pilotrunapp.com/templates_c/
```

### **問題 2: 磁盤空間不足**
**檢查**:
```bash
df -h /data/wwwroot/test.pilotrunapp.com/
```

### **問題 3: 模板語法錯誤**
檢查模板中是否有：
- 未關閉的 `{if}` 語句
- 拼寫錯誤的變數名稱
- 缺少的 `{/foreach}` 標籤

---

## 🔍 調試方法

### **啟用 Smarty 調試模式**
```php
$smarty->debugging = true;
$smarty->error_reporting = E_ALL;
```

### **檢查編譯後的 PHP 文件**
```bash
# 查看編譯錯誤的具體行
head -1790 /data/wwwroot/test.pilotrunapp.com/templates_c/ee6dc9d397d00d2df15443e4b8b8242212d47452_0.file.MissionsManageRonnie.tpl.php | tail -10
```

### **檢查 PHP 錯誤日誌**
```bash
tail -f /var/log/php_errors.log
# 或
tail -f /data/wwwroot/test.pilotrunapp.com/error.log
```

---

## 🎯 臨時解決方案

如果問題持續存在，可以臨時禁用 Smarty 緩存：

```php
// 在 maininit.php 中臨時添加
$smarty->force_compile = true;
$smarty->caching = false;
$smarty->compile_check = true;
```

**注意**: 這會影響性能，僅用於調試。

---

## ✅ 驗證步驟

1. **清理緩存後**，訪問頁面檢查是否還有錯誤
2. **檢查新的編譯文件**是否正確生成
3. **確認功能正常**，所有變數都能正確顯示

---

## 📋 預防措施

1. **定期清理**: 設置定期清理編譯緩存的 cron job
2. **權限監控**: 定期檢查目錄權限
3. **模板驗證**: 在部署前驗證模板語法
4. **版本控制**: 確保模板文件正確版本控制

---

**如果問題仍然存在，請提供具體的錯誤訊息以進行進一步診斷。**