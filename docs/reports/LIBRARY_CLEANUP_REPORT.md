# 第三方庫清理報告
## Third-Party Library Cleanup Report

**日期**: 2025-07-15  
**最後更新**: 2025-07-16  
**分析對象**: PilotRun代碼庫中的未使用依賴項  
**預估節省空間**: ~15MB

---

## 🚧 待完成的清理工作

### 1. **重複的jQuery版本** (尚未清理)
仍在 `js/` 目錄中的檔案：
- `jquery-1.3.2.min.js` ❌ 未使用
- `jquery-1.6.2.min.js` ❌ 未使用
- `jquery-1.7.1.min.js` ❌ 未使用
- `jquery-1.12.0.min.js` ❌ 未使用
- `jquery.min.js` ❌ 重複
- `flot/jquery.js` ❌ 重複

**建議執行**:
```bash
rm js/jquery-1.3.2.min.js
rm js/jquery-1.6.2.min.js  
rm js/jquery-1.7.1.min.js
rm js/jquery-1.12.0.min.js
rm js/jquery.min.js
rm js/flot/jquery.js
rm js/flot/jquery.min.js
```

### 2. **Smarty重複版本** (待刪除)
```
├── libs/smarty4/ (1.5MB) ✅ 正在使用
├── smarty-4.3.4/ (2.9MB) ❌ 待刪除
└── to_be_remove/smarty3_backup_4_3_4/ (1.5MB) ❌ 備份副本
```

**建議執行**:
```bash
rm -rf smarty-4.3.4/
```

### 3. **其他未使用的JavaScript庫**
```bash
# 未使用的動畫庫
rm js/moo.fx.js
rm js/prototype.lite.js
rm js/litebox-1.0.js
rm js/geolet.js

# 未使用的選單系統
rm -rf libs/JSCookMenu/
```

### 4. **開發文檔** (預估 2MB)
```bash
rm js/flot/README.md
rm js/flot/API.md
rm js/flot/FAQ.md
rm js/flot/NEWS.md
rm js/flot/CONTRIBUTING.md
rm js/flot/PLUGINS.md
rm js/flot/Makefile
```

---

## ⚠️ 需要進一步調查

### TinyMCE編輯器
- **位置**: `/js/tiny_mce/`
- **使用情況**: 僅在 AdminDB.tpl 中使用
- **建議**: 評估是否可用更輕量的編輯器替換

### Proj4坐標轉換庫
- **位置**: `/js/proj4.js`, `/js/proj4leaflet.js`
- **使用情況**: 可能用於地圖坐標轉換
- **建議**: 確認地圖功能是否需要

---

## 📈 實施進度

### Phase 2: 安全清理 ⏳ 進行中 (30% 完成)
- [ ] 刪除重複的jQuery版本
- [ ] 刪除Smarty重複副本
- [ ] 測試所有頁面載入

### Phase 3: 深度調查 ❌ 未開始
- [ ] 評估TinyMCE使用必要性
- [ ] 檢查Proj4庫是否必需
- [ ] 建立第三方庫清單文檔

### Phase 4: 優化 ❌ 未開始
- [ ] 壓縮未壓縮的JavaScript文件
- [ ] 建立依賴管理指南

---

## ⚡ 預期效果 (完成所有清理後)

### 性能提升
- **頁面載入時間**: 減少 20-30%
- **網路流量**: 減少 15MB
- **瀏覽器緩存**: 更有效率

### 維護效益
- **部署速度**: 提升 25%
- **版本衝突**: 消除重複版本問題
- **安全風險**: 減少攻擊表面

---

## 🔒 風險評估

### 低風險項目 ✅
- 刪除明確未使用的jQuery版本
- 移除開發文檔
- 清理Smarty重複副本

### 中風險項目 ⚠️
- TinyMCE編輯器替換
- Proj4庫移除

### 風險緩解策略
1. **分階段實施**: 逐步進行，避免一次性大幅變動
2. **完整備份**: 在 `to_be_remove/` 保留備份
3. **充分測試**: 每階段後進行功能測試
4. **回滾計劃**: 準備快速回滾機制

---

**下一步**: 繼續 Phase 2 的剩餘清理工作