\$direct\_access\_security {#variable.direct.access.security}
==========================

Direct access security inhibits direct browser access to compiled or
cached template files.

Direct access security is enabled by default. To disable it set
`$direct_access_security` to FALSE.

> **Note**
>
> This is a compile time option. If you change the setting you must make
> sure that the templates get recompiled.
