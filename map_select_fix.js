/**
 * 地圖選擇修復腳本
 * 在瀏覽器控制台中運行此腳本來診斷和修復地圖選擇問題
 */

(function() {
    console.log('=== 地圖選擇修復腳本開始執行 ===');
    
    // 檢查 jQuery 是否可用
    if (typeof $ === 'undefined') {
        console.error('jQuery 未加載，無法執行修復腳本');
        return;
    }
    
    console.log('jQuery 版本:', $.fn.jquery);
    
    // 查找地圖選單元素
    const $mapSelect = $('#wizard_g_map_type');
    
    if ($mapSelect.length === 0) {
        console.error('找不到地圖選單元素 #wizard_g_map_type');
        return;
    }
    
    console.log('找到地圖選單元素');
    
    // 診斷當前狀態
    function diagnoseMapSelect() {
        console.log('=== 診斷地圖選單狀態 ===');
        
        const element = $mapSelect[0];
        const computedStyle = window.getComputedStyle(element);
        
        console.log('基本信息:');
        console.log('- 元素存在:', $mapSelect.length > 0);
        console.log('- 可見性:', $mapSelect.is(':visible'));
        console.log('- 當前值:', $mapSelect.val());
        console.log('- 選項數量:', $mapSelect.find('option').length);
        
        console.log('CSS 樣式:');
        console.log('- display:', computedStyle.display);
        console.log('- visibility:', computedStyle.visibility);
        console.log('- opacity:', computedStyle.opacity);
        console.log('- color:', computedStyle.color);
        console.log('- background-color:', computedStyle.backgroundColor);
        console.log('- appearance:', computedStyle.appearance || computedStyle.webkitAppearance);
        console.log('- height:', computedStyle.height);
        console.log('- width:', computedStyle.width);
        
        console.log('選項內容:');
        $mapSelect.find('option').each(function(index) {
            const $option = $(this);
            console.log(`- 選項 ${index}: 值="${$option.val()}" 文字="${$option.text()}"`);
        });
        
        return {
            visible: $mapSelect.is(':visible'),
            hasOptions: $mapSelect.find('option').length > 0,
            currentValue: $mapSelect.val(),
            appearance: computedStyle.appearance || computedStyle.webkitAppearance
        };
    }
    
    // 修復地圖選單
    function fixMapSelect() {
        console.log('=== 開始修復地圖選單 ===');
        
        // 1. 移除可能導致問題的樣式
        $mapSelect.css({
            'appearance': 'menulist',
            '-webkit-appearance': 'menulist',
            '-moz-appearance': 'menulist',
            'color': '#495057',
            'background-color': '#fff',
            'visibility': 'visible',
            'opacity': '1',
            'display': 'block'
        });
        
        // 2. 確保選項可見
        $mapSelect.find('option').css({
            'color': '#495057',
            'background-color': '#fff',
            'display': 'block'
        });
        
        // 3. 如果選單為空，設置默認值
        if (!$mapSelect.val() && $mapSelect.find('option[value!=""]').length > 0) {
            const firstOption = $mapSelect.find('option[value!=""]').first();
            console.log('設置默認值:', firstOption.val());
            $mapSelect.val(firstOption.val());
        }
        
        // 4. 重新綁定事件處理器
        $mapSelect.off('change.fix').on('change.fix', function() {
            const value = $(this).val();
            const text = $(this).find('option:selected').text();
            console.log('地圖選擇變更:', text, '(值:', value, ')');
            
            // 標記用戶選擇
            $(this).data('user-selected', true);
            
            // 驗證選擇是否保持
            setTimeout(() => {
                const currentValue = $(this).val();
                if (currentValue === value) {
                    console.log('✓ 選擇保持正常');
                } else {
                    console.error('✗ 選擇被重置！期望:', value, '實際:', currentValue);
                }
            }, 100);
        });
        
        console.log('修復完成');
    }
    
    // 測試選單功能
    function testMapSelect() {
        console.log('=== 測試地圖選單功能 ===');
        
        const options = $mapSelect.find('option[value!=""]');
        if (options.length === 0) {
            console.error('沒有可用的選項進行測試');
            return;
        }
        
        const testValue = options.first().val();
        console.log('測試選擇值:', testValue);
        
        $mapSelect.val(testValue).trigger('change');
        
        setTimeout(() => {
            const currentValue = $mapSelect.val();
            const currentText = $mapSelect.find('option:selected').text();
            
            if (currentValue === testValue) {
                console.log('✓ 測試成功！當前選擇:', currentText, '(值:', currentValue, ')');
            } else {
                console.error('✗ 測試失敗！期望值:', testValue, '實際值:', currentValue);
            }
        }, 200);
    }
    
    // 創建修復按鈕
    function createFixButton() {
        if ($('#map-fix-button').length > 0) {
            return; // 按鈕已存在
        }
        
        const $button = $('<button>')
            .attr('id', 'map-fix-button')
            .text('修復地圖選單')
            .css({
                'position': 'fixed',
                'top': '10px',
                'right': '10px',
                'z-index': '9999',
                'background': '#007bff',
                'color': 'white',
                'border': 'none',
                'padding': '10px 15px',
                'border-radius': '5px',
                'cursor': 'pointer'
            })
            .on('click', function() {
                diagnoseMapSelect();
                fixMapSelect();
                testMapSelect();
            });
        
        $('body').append($button);
        console.log('已添加修復按鈕到頁面右上角');
    }
    
    // 執行診斷和修復
    const diagnosis = diagnoseMapSelect();
    
    if (!diagnosis.visible || !diagnosis.hasOptions) {
        console.warn('檢測到問題，執行修復...');
        fixMapSelect();
    }
    
    // 創建修復按鈕
    createFixButton();
    
    // 測試功能
    setTimeout(() => {
        testMapSelect();
    }, 500);
    
    console.log('=== 地圖選擇修復腳本執行完成 ===');
    console.log('如果問題仍然存在，請點擊頁面右上角的"修復地圖選單"按鈕');
    
    // 將修復函數暴露到全局作用域
    window.fixMapSelect = function() {
        diagnoseMapSelect();
        fixMapSelect();
        testMapSelect();
    };
    
    console.log('可以在控制台中運行 fixMapSelect() 來重新執行修復');
})();
