# 地圖選擇問題最終解決方案

## 問題現狀

雖然日誌顯示修復代碼已正常加載，但網頁上的地圖選單仍然顯示空白。這表明問題可能與 CSS 樣式衝突有關。

## 立即解決方案

### 方案 1: 使用修復腳本（推薦）

1. **打開瀏覽器開發者工具**
   - 按 F12 或右鍵選擇"檢查元素"
   - 切換到 "Console" 標籤

2. **運行修復腳本**
   ```javascript
   // 複製並貼上以下代碼到控制台中執行
   (function() {
       const $mapSelect = $('#wizard_g_map_type');
       if ($mapSelect.length === 0) {
           console.error('找不到地圖選單');
           return;
       }
       
       // 強制修復樣式
       $mapSelect.css({
           'appearance': 'menulist !important',
           '-webkit-appearance': 'menulist !important',
           '-moz-appearance': 'menulist !important',
           'color': '#495057 !important',
           'background-color': '#fff !important',
           'visibility': 'visible !important',
           'opacity': '1 !important'
       });
       
       // 修復選項樣式
       $mapSelect.find('option').css({
           'color': '#495057 !important',
           'background-color': '#fff !important'
       });
       
       console.log('地圖選單修復完成');
   })();
   ```

3. **驗證修復效果**
   - 檢查地圖選單是否可見
   - 嘗試選擇不同的地圖類型
   - 確認選擇後不會重置

### 方案 2: 使用完整修復腳本

1. **下載修復腳本**
   - 使用提供的 `map_select_fix.js` 文件

2. **在控制台中加載腳本**
   ```javascript
   // 方法 1: 直接複製 map_select_fix.js 的內容到控制台執行
   
   // 方法 2: 如果腳本在服務器上，可以動態加載
   const script = document.createElement('script');
   script.src = 'map_select_fix.js';
   document.head.appendChild(script);
   ```

3. **使用修復按鈕**
   - 腳本會在頁面右上角添加一個"修復地圖選單"按鈕
   - 點擊按鈕即可執行修復

### 方案 3: 臨時 CSS 修復

在瀏覽器開發者工具的 "Elements" 標籤中：

1. **找到地圖選單元素**
   - 搜索 `id="wizard_g_map_type"`

2. **添加內聯樣式**
   ```html
   <select id="wizard_g_map_type" name="g_map_type" 
           style="appearance: menulist !important; 
                  -webkit-appearance: menulist !important; 
                  color: #495057 !important; 
                  background-color: #fff !important;">
   ```

## 永久解決方案

### 1. 服務器端修復

已經在 `templates/GameManage.tpl` 中實施了以下修復：

- 添加了內聯樣式到 HTML 元素
- 移除了 `appearance: none` 樣式
- 添加了 JavaScript 強制樣式修復
- 實施了用戶選擇標記機制

### 2. 清除緩存

```bash
# 服務器端清除模板緩存
rm -rf templates_c/*

# 瀏覽器端強制刷新
Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac)
```

### 3. 檢查文件權限

```bash
chmod 755 templates_c/
chown -R www-data:www-data templates_c/
```

## 診斷工具

### 1. 檢查修復是否生效

在控制台中運行：
```javascript
console.log('修復版本:', $('#wizard_g_map_type').length > 0 ? '已加載' : '未找到');
console.log('選單可見:', $('#wizard_g_map_type').is(':visible'));
console.log('選項數量:', $('#wizard_g_map_type option').length);
```

### 2. 檢查 CSS 樣式

```javascript
const element = document.getElementById('wizard_g_map_type');
const style = window.getComputedStyle(element);
console.log('appearance:', style.appearance);
console.log('color:', style.color);
console.log('background:', style.backgroundColor);
```

### 3. 使用調試頁面

- `simple_map_test.html` - 測試不同樣式的選單
- `debug_map_selection.html` - 詳細功能測試
- `debug_server_status.php` - 服務器狀態檢查

## 常見問題排除

### Q: 修復腳本運行後仍然看不到選單
**A:** 嘗試以下步驟：
1. 檢查是否有其他 CSS 覆蓋了樣式
2. 嘗試使用 `!important` 強制應用樣式
3. 檢查瀏覽器兼容性

### Q: 選單可見但選擇後仍然重置
**A:** 檢查以下項目：
1. 確認用戶選擇標記機制是否正常工作
2. 檢查是否有其他 JavaScript 干擾
3. 查看控制台是否有錯誤訊息

### Q: 修復在某些瀏覽器上不起作用
**A:** 不同瀏覽器的處理方式：
- **Chrome/Safari**: 使用 `-webkit-appearance: menulist`
- **Firefox**: 使用 `-moz-appearance: menulist`
- **Edge**: 使用 `appearance: menulist`

## 技術細節

### 問題根源
1. **CSS appearance: none** - 移除了原生選單樣式
2. **樣式衝突** - 自定義樣式與瀏覽器默認樣式衝突
3. **JavaScript 干擾** - 草稿恢復邏輯覆蓋用戶選擇

### 修復原理
1. **恢復原生外觀** - 使用 `appearance: menulist`
2. **強制樣式** - 使用 `!important` 確保樣式優先級
3. **用戶選擇保護** - 標記用戶手動選擇，防止被覆蓋

## 聯繫支援

如果以上解決方案都無法解決問題，請提供以下信息：

1. **瀏覽器信息** - 版本、類型
2. **控制台錯誤** - 任何 JavaScript 錯誤
3. **網絡請求** - 檢查資源是否正確加載
4. **螢幕截圖** - 問題的視覺表現

## 預防措施

為避免類似問題再次發生：

1. **謹慎使用 appearance: none**
2. **測試多種瀏覽器**
3. **保留原生控件樣式的備份**
4. **使用漸進增強而非完全替換**
