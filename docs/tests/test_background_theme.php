<?php
/**
 * 背景主題功能測試腳本
 * 用途：驗證背景主題功能完整性
 * 執行前請確保已執行 sql/add_background_theme_columns.sql
 */

header('Content-Type: text/html; charset=utf-8');
require_once 'inc/maininit.php';

echo "<h2>背景主題功能測試</h2>";

// 1. 檢查資料庫欄位是否存在
echo "<h3>1. 檢查資料庫欄位</h3>";
$sql = "SHOW COLUMNS FROM `game` LIKE '%theme%' OR SHOW COLUMNS FROM `game` LIKE '%background%'";
$result = $db->query("SHOW COLUMNS FROM `game` WHERE Field IN ('theme_type', 'background_status', 'background_image', 'background_thumb')");
$columns = $db->fetch_array_all($result);

if (count($columns) == 4) {
    echo "✅ 所有背景主題欄位已正確添加到資料庫<br>";
    foreach ($columns as $column) {
        echo "　　- {$column['Field']}: {$column['Type']} (預設: {$column['Default']})<br>";
    }
} else {
    echo "❌ 背景主題欄位缺失，請執行 sql/add_background_theme_columns.sql<br>";
    echo "　　找到欄位數量: " . count($columns) . " (應該是 4 個)<br>";
}

// 2. 測試 API 回應
echo "<h3>2. 測試 API 回應</h3>";
$sql = "SELECT g_id, theme_type, background_status, background_image, background_thumb FROM `game` LIMIT 1";
$result = $db->query($sql);
$game = $db->fetch_array($result);

if ($game) {
    echo "✅ API 可以正確讀取背景主題欄位<br>";
    echo "　　遊戲 ID: {$game['g_id']}<br>";
    echo "　　主題類型: {$game['theme_type']}<br>";
    echo "　　背景狀態: {$game['background_status']}<br>";
    echo "　　背景圖片: " . ($game['background_image'] ?: '未設定') . "<br>";
    echo "　　背景縮圖: " . ($game['background_thumb'] ?: '未設定') . "<br>";
} else {
    echo "❌ 無法讀取遊戲資料，請檢查資料庫<br>";
}

// 3. 測試主題配置
echo "<h3>3. 測試主題配置</h3>";
$themes = [
    'original' => '原始純主題',
    'default' => '經典藍', 
    'marvel' => '漫威紅',
    'harry_potter' => '魔法金',
    'adventure' => '探險綠',
    'mystery' => '神秘紫',
    'ocean' => '海洋藍'
];

echo "✅ 支援的主題類型:<br>";
foreach ($themes as $key => $name) {
    echo "　　- {$key}: {$name}<br>";
}

// 4. 測試縮圖生成功能
echo "<h3>4. 測試縮圖生成功能</h3>";
if (function_exists('imagecreatefromjpeg') && function_exists('imagecreatetruecolor')) {
    echo "✅ GD 圖片處理庫已安裝，支援縮圖生成<br>";
    echo "　　支援格式: JPEG";
    if (function_exists('imagecreatefrompng')) echo ", PNG";
    if (function_exists('imagecreatefromwebp')) echo ", WebP";
    echo "<br>";
} else {
    echo "❌ GD 圖片處理庫未安裝，無法生成縮圖<br>";
}

// 5. 檢查目錄權限
echo "<h3>5. 檢查上傳目錄權限</h3>";
$uploadDir = "UploadManageImage";
if (is_dir($uploadDir) && is_writable($uploadDir)) {
    echo "✅ 上傳目錄 '{$uploadDir}' 存在且可寫入<br>";
} else {
    echo "❌ 上傳目錄 '{$uploadDir}' 不存在或無寫入權限<br>";
}

// 6. 模擬 API 請求測試
echo "<h3>6. 模擬 API 請求測試</h3>";
if ($game) {
    $mockResponse = [
        'flag' => '1',
        'msg' => 'success',
        'data' => [
            'g_id' => $game['g_id'],
            'theme_type' => $game['theme_type'],
            'background_status' => $game['background_status'],
            'background_image' => $game['background_image'],
            'background_thumb' => $game['background_thumb']
        ]
    ];
    
    echo "✅ 模擬 API 回應格式正確:<br>";
    echo "<pre>" . json_encode($mockResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
}

echo "<hr>";
echo "<h3>測試結果總結</h3>";
echo "如果所有項目都顯示 ✅，則背景主題功能已成功實現。<br>";
echo "您可以在遊戲管理頁面測試背景主題設定功能。<br>";
echo "<br><strong>下一步:</strong><br>";
echo "1. 在遊戲管理頁面設定不同主題<br>";
echo "2. 上傳背景圖片測試<br>";
echo "3. 在手機端驗證主題顯示效果<br>";

?>