{"name": "smarty/smarty", "type": "library", "description": "Smarty - the compiling PHP template engine", "keywords": ["templating"], "homepage": "https://smarty-php.github.io/smarty/", "license": "LGPL-3.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.iwink.nl/"}], "support": {"issues": "https://github.com/smarty-php/smarty/issues", "forum": "https://github.com/smarty-php/smarty/discussions"}, "require": {"php": "^7.1 || ^8.0"}, "autoload": {"classmap": ["libs/"]}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "require-dev": {"phpunit/phpunit": "^8.5 || ^7.5", "smarty/smarty-lexer": "^3.1"}}