<?php
/**
 * 背景圖片問題排查腳本
 * 用途：檢查背景圖片從資料庫到API到手機端的完整流程
 */

header('Content-Type: text/html; charset=utf-8');
require_once 'inc/maininit.php';

echo "<h2>🔍 背景圖片問題排查</h2>";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { color: #4CAF50; font-weight: bold; }
.error { color: #f44336; font-weight: bold; }
.warning { color: #ff9800; font-weight: bold; }
.info { color: #2196F3; font-weight: bold; }
pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 1. 檢查資料庫中的背景圖片資料
echo "<div class='debug-section'>";
echo "<h3>1. 📊 檢查資料庫資料</h3>";

$sql = "SELECT g_id, g_title, theme_type, background_status, background_image, background_thumb FROM `game` ORDER BY g_id DESC LIMIT 5";
$result = $db->query($sql);
$games = $db->fetch_array_all($result);

if ($games) {
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>遊戲ID</th><th>遊戲標題</th><th>主題類型</th><th>背景狀態</th><th>背景圖片</th><th>背景縮圖</th>";
    echo "</tr>";
    
    foreach ($games as $game) {
        echo "<tr>";
        echo "<td>{$game['g_id']}</td>";
        echo "<td>" . htmlspecialchars($game['g_title']) . "</td>";
        echo "<td>{$game['theme_type']}</td>";
        echo "<td class='" . ($game['background_status'] == 'active' ? 'success' : 'warning') . "'>{$game['background_status']}</td>";
        echo "<td>" . ($game['background_image'] ? "<span class='success'>✓</span> " . htmlspecialchars($game['background_image']) : "<span class='error'>✗ 無圖片</span>") . "</td>";
        echo "<td>" . ($game['background_thumb'] ? "<span class='success'>✓</span> " . htmlspecialchars($game['background_thumb']) : "<span class='error'>✗ 無縮圖</span>") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='error'>❌ 無法讀取遊戲資料</span>";
}
echo "</div>";

// 2. 模擬 API 請求
echo "<div class='debug-section'>";
echo "<h3>2. 🔗 模擬 API 請求</h3>";

if ($games && count($games) > 0) {
    $testGame = $games[0]; // 使用第一個遊戲進行測試
    
    echo "<p><strong>測試遊戲:</strong> {$testGame['g_title']} (ID: {$testGame['g_id']})</p>";
    
    // 模擬 GameList API 查詢
    $apiSql = "SELECT g.g_id,g.g_content,g.g_title,g.g_type,g.g_photo,g.g_begin_date,g.g_begin_time,g.g_last_hour,g.g_last_minute,g.g_add,g.g_feed,g.g_crowd,g.g_share_photo_all_team,g.g_user_logo,g.g_app_sharephoto,g.theme_type,g.background_status,g.background_image,g.background_thumb FROM `game` as g WHERE g.g_id = {$testGame['g_id']}";
    
    $apiResult = $db->query($apiSql);
    $apiData = $db->fetch_array($apiResult);
    
    if ($apiData) {
        echo "<span class='success'>✅ API 查詢成功</span><br>";
        echo "<strong>API 回應的背景資料:</strong><br>";
        echo "<pre>";
        echo "theme_type: " . ($apiData['theme_type'] ?: 'NULL') . "\n";
        echo "background_status: " . ($apiData['background_status'] ?: 'NULL') . "\n";
        echo "background_image: " . ($apiData['background_image'] ?: 'NULL') . "\n";
        echo "background_thumb: " . ($apiData['background_thumb'] ?: 'NULL') . "\n";
        echo "</pre>";
        
        // 模擬完整的 JSON 回應
        $mockApiResponse = [
            'flag' => '1',
            'msg' => 'success',
            'game_list' => [$apiData]
        ];
        
        echo "<strong>完整 API JSON 回應:</strong><br>";
        echo "<pre>" . json_encode($mockApiResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
        
    } else {
        echo "<span class='error'>❌ API 查詢失敗</span>";
    }
} else {
    echo "<span class='warning'>⚠️ 沒有遊戲資料可測試</span>";
}
echo "</div>";

// 3. 檢查圖片檔案是否存在
echo "<div class='debug-section'>";
echo "<h3>3. 📁 檢查圖片檔案</h3>";

if (isset($apiData) && $apiData['background_image']) {
    $imagePath = $apiData['background_image'];
    $fullImagePath = '/data/wwwroot/test.pilotrunapp.com' . $imagePath;
    
    echo "<p><strong>圖片路徑:</strong> {$imagePath}</p>";
    echo "<p><strong>完整路徑:</strong> {$fullImagePath}</p>";
    
    if (file_exists($fullImagePath)) {
        $fileSize = filesize($fullImagePath);
        $imageInfo = getimagesize($fullImagePath);
        
        echo "<span class='success'>✅ 圖片檔案存在</span><br>";
        echo "<strong>檔案資訊:</strong><br>";
        echo "- 檔案大小: " . round($fileSize/1024, 2) . " KB<br>";
        if ($imageInfo) {
            echo "- 圖片尺寸: {$imageInfo[0]} x {$imageInfo[1]} pixels<br>";
            echo "- 圖片類型: {$imageInfo['mime']}<br>";
        }
        
        // 檢查縮圖
        if ($apiData['background_thumb']) {
            $thumbPath = '/data/wwwroot/test.pilotrunapp.com' . $apiData['background_thumb'];
            if (file_exists($thumbPath)) {
                echo "<span class='success'>✅ 縮圖檔案存在</span><br>";
            } else {
                echo "<span class='error'>❌ 縮圖檔案不存在: {$thumbPath}</span><br>";
            }
        }
        
    } else {
        echo "<span class='error'>❌ 圖片檔案不存在: {$fullImagePath}</span><br>";
    }
} else {
    echo "<span class='warning'>⚠️ 沒有背景圖片路徑可檢查</span>";
}
echo "</div>";

// 4. 檢查網路可訪問性
echo "<div class='debug-section'>";
echo "<h3>4. 🌐 檢查網路可訪問性</h3>";

if (isset($imagePath)) {
    $testUrl = "https://test.pilotrunapp.com" . $imagePath;
    echo "<p><strong>測試 URL:</strong> <a href='{$testUrl}' target='_blank'>{$testUrl}</a></p>";
    
    // 使用 cURL 檢查 URL 是否可訪問
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode == 200) {
        echo "<span class='success'>✅ URL 可正常訪問 (HTTP {$httpCode})</span><br>";
    } else {
        echo "<span class='error'>❌ URL 無法訪問 (HTTP {$httpCode})</span><br>";
        if ($error) {
            echo "<span class='error'>錯誤訊息: {$error}</span><br>";
        }
    }
} else {
    echo "<span class='warning'>⚠️ 沒有圖片 URL 可測試</span>";
}
echo "</div>";

// 5. 手機端檢查建議
echo "<div class='debug-section'>";
echo "<h3>5. 📱 手機端檢查建議</h3>";
echo "<p>如果以上檢查都正常，請在手機端檢查以下項目：</p>";
echo "<ol>";
echo "<li><strong>網路連線</strong> - 確保手機可以訪問 test.pilotrunapp.com</li>";
echo "<li><strong>Flutter Console</strong> - 查看是否有圖片載入錯誤訊息</li>";
echo "<li><strong>快取問題</strong> - 嘗試清除 app 快取或重新安裝</li>";
echo "<li><strong>API 回應</strong> - 確認手機端收到的 API 資料包含 background_image 欄位</li>";
echo "<li><strong>Provider 狀態</strong> - 檢查 ThemeProvider 中的 effectiveBackgroundImage 是否有值</li>";
echo "</ol>";

echo "<h4>🔧 建議的調試步驟：</h4>";
echo "<pre>";
echo "1. 在 Flutter app 中添加以下調試代碼：
   print('🎨 Background Image: \${themeProvider.effectiveBackgroundImage}');
   print('🎨 Theme Type: \${themeProvider.currentThemeType}');
   print('🎨 Background Enabled: \${themeProvider.backgroundEnabled}');

2. 檢查 CachedNetworkImage 的錯誤回調：
   errorWidget: (context, url, error) {
     print('❌ Image Load Error: \$error');
     print('❌ Failed URL: \$url');
     return _buildThemeBackground(context);
   }

3. 驗證 API 回應：
   檢查 GameContentProvider 中是否正確解析了 background_image 欄位
";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>📋 排查結果總結</h3>";
echo "<p>請檢查以上各個步驟的結果，找出問題所在。如果資料庫和檔案都正常，問題可能在於：</p>";
echo "<ul>";
echo "<li>API 回應格式問題</li>";
echo "<li>手機端網路存取問題</li>";
echo "<li>Flutter 圖片載入邏輯問題</li>";
echo "<li>快取或狀態管理問題</li>";
echo "</ul>";

?>