/* Clearlooks 2 */

/* Reset */
.clearlooks2, .clearlooks2 div, .clearlooks2 span, .clearlooks2 a {vertical-align:baseline; text-align:left; position:absolute; border:0; padding:0; margin:0; background:transparent; font-family:<PERSON><PERSON>,<PERSON>erd<PERSON>; font-size:11px; color:#000; text-decoration:none; font-weight:normal; width:auto; height:auto; overflow:hidden; display:block}

/* General */
.clearlooks2 {position:absolute; direction:ltr}
.clearlooks2 .mceWrapper {position:static}
.mceEventBlocker {position:fixed; left:0; top:0; background:url(img/horizontal.gif) no-repeat 0 -75px; width:100%; height:100%}
.clearlooks2 .mcePlaceHolder {border:1px solid #000; background:#888; top:0; left:0; opacity:0.5; -ms-filter:'alpha(opacity=50)'; filter:alpha(opacity=50)}
.clearlooks2_modalBlocker {position:fixed; left:0; top:0; width:100%; height:100%; background:#FFF; opacity:0.6; -ms-filter:'alpha(opacity=60)'; filter:alpha(opacity=60); display:none}

/* Top */
.clearlooks2 .mceTop, .clearlooks2 .mceTop div {top:0; width:100%; height:23px}
.clearlooks2 .mceTop .mceLeft {width:6px; background:url(img/corners.gif)}
.clearlooks2 .mceTop .mceCenter {right:6px; width:100%; height:23px; background:url(img/horizontal.gif) 12px 0; clip:rect(auto auto auto 12px)}
.clearlooks2 .mceTop .mceRight {right:0; width:6px; height:23px; background:url(img/corners.gif) -12px 0}
.clearlooks2 .mceTop span {width:100%; text-align:center; vertical-align:middle; line-height:23px; font-weight:bold}
.clearlooks2 .mceFocus .mceTop .mceLeft {background:url(img/corners.gif) -6px 0}
.clearlooks2 .mceFocus .mceTop .mceCenter {background:url(img/horizontal.gif) 0 -23px}
.clearlooks2 .mceFocus .mceTop .mceRight {background:url(img/corners.gif) -18px 0}
.clearlooks2 .mceFocus .mceTop span {color:#FFF}

/* Middle */
.clearlooks2 .mceMiddle, .clearlooks2 .mceMiddle div {top:0}
.clearlooks2 .mceMiddle {width:100%; height:100%; clip:rect(23px auto auto auto)}
.clearlooks2 .mceMiddle .mceLeft {left:0; width:5px; height:100%; background:url(img/vertical.gif) -5px 0}
.clearlooks2 .mceMiddle span {top:23px; left:5px; width:100%; height:100%; background:#FFF}
.clearlooks2 .mceMiddle .mceRight {right:0; width:5px; height:100%; background:url(img/vertical.gif)}

/* Bottom */
.clearlooks2 .mceBottom, .clearlooks2 .mceBottom div {height:6px}
.clearlooks2 .mceBottom {left:0; bottom:0; width:100%}
.clearlooks2 .mceBottom div {top:0}
.clearlooks2 .mceBottom .mceLeft {left:0; width:5px; background:url(img/corners.gif) -34px -6px}
.clearlooks2 .mceBottom .mceCenter {left:5px; width:100%; background:url(img/horizontal.gif) 0 -46px}
.clearlooks2 .mceBottom .mceRight {right:0; width:5px; background: url(img/corners.gif) -34px 0}
.clearlooks2 .mceBottom span {display:none}
.clearlooks2 .mceStatusbar .mceBottom, .clearlooks2 .mceStatusbar .mceBottom div {height:23px}
.clearlooks2 .mceStatusbar .mceBottom .mceLeft {background:url(img/corners.gif) -29px 0}
.clearlooks2 .mceStatusbar .mceBottom .mceCenter {background:url(img/horizontal.gif) 0 -52px}
.clearlooks2 .mceStatusbar .mceBottom .mceRight {background:url(img/corners.gif) -24px 0}
.clearlooks2 .mceStatusbar .mceBottom span {display:block; left:7px; font-family:Arial, Verdana; font-size:11px; line-height:23px}

/* Actions */
.clearlooks2 a {width:29px; height:16px; top:3px;}
.clearlooks2 .mceClose {right:6px; background:url(img/buttons.gif) -87px 0}
.clearlooks2 .mceMin {display:none; right:68px; background:url(img/buttons.gif) 0 0}
.clearlooks2 .mceMed {display:none; right:37px; background:url(img/buttons.gif) -29px 0}
.clearlooks2 .mceMax {display:none; right:37px; background:url(img/buttons.gif) -58px 0}
.clearlooks2 .mceMove {display:none;width:100%;cursor:move;background:url(img/corners.gif) no-repeat -100px -100px}
.clearlooks2 .mceMovable .mceMove {display:block}
.clearlooks2 .mceFocus .mceClose {right:6px; background:url(img/buttons.gif) -87px -16px}
.clearlooks2 .mceFocus .mceMin {right:68px; background:url(img/buttons.gif) 0 -16px}
.clearlooks2 .mceFocus .mceMed {right:37px; background:url(img/buttons.gif) -29px -16px}
.clearlooks2 .mceFocus .mceMax {right:37px; background:url(img/buttons.gif) -58px -16px}
.clearlooks2 .mceFocus .mceClose:hover {right:6px; background:url(img/buttons.gif) -87px -32px}
.clearlooks2 .mceFocus .mceClose:hover {right:6px; background:url(img/buttons.gif) -87px -32px}
.clearlooks2 .mceFocus .mceMin:hover {right:68px; background:url(img/buttons.gif) 0 -32px}
.clearlooks2 .mceFocus .mceMed:hover {right:37px; background:url(img/buttons.gif) -29px -32px}
.clearlooks2 .mceFocus .mceMax:hover {right:37px; background:url(img/buttons.gif) -58px -32px}

/* Resize */
.clearlooks2 .mceResize {top:auto; left:auto; display:none; width:5px; height:5px; background:url(img/horizontal.gif) no-repeat 0 -75px}
.clearlooks2 .mceResizable .mceResize {display:block}
.clearlooks2 .mceResizable .mceMin, .clearlooks2 .mceMax {display:none}
.clearlooks2 .mceMinimizable .mceMin {display:block}
.clearlooks2 .mceMaximizable .mceMax {display:block}
.clearlooks2 .mceMaximized .mceMed {display:block}
.clearlooks2 .mceMaximized .mceMax {display:none}
.clearlooks2 a.mceResizeN {top:0; left:0; width:100%; cursor:n-resize}
.clearlooks2 a.mceResizeNW {top:0; left:0; cursor:nw-resize}
.clearlooks2 a.mceResizeNE {top:0; right:0; cursor:ne-resize}
.clearlooks2 a.mceResizeW {top:0; left:0; height:100%; cursor:w-resize;}
.clearlooks2 a.mceResizeE {top:0; right:0; height:100%; cursor:e-resize}
.clearlooks2 a.mceResizeS {bottom:0; left:0; width:100%; cursor:s-resize}
.clearlooks2 a.mceResizeSW {bottom:0; left:0; cursor:sw-resize}
.clearlooks2 a.mceResizeSE {bottom:0; right:0; cursor:se-resize}

/* Alert/Confirm */
.clearlooks2 .mceButton {font-weight:bold; bottom:10px; width:80px; height:30px; background:url(img/button.gif); line-height:30px; vertical-align:middle; text-align:center; outline:0}
.clearlooks2 .mceMiddle .mceIcon {left:15px; top:35px; width:32px; height:32px}
.clearlooks2 .mceAlert .mceMiddle span, .clearlooks2 .mceConfirm .mceMiddle span {background:transparent;left:60px; top:35px; width:320px; height:50px; font-weight:bold; overflow:auto; white-space:normal}
.clearlooks2 a:hover {font-weight:bold;}
.clearlooks2 .mceAlert .mceMiddle, .clearlooks2 .mceConfirm .mceMiddle {background:#D6D7D5}
.clearlooks2 .mceAlert .mceOk {left:50%; top:auto; margin-left: -40px}
.clearlooks2 .mceAlert .mceIcon {background:url(img/alert.gif)}
.clearlooks2 .mceConfirm .mceOk {left:50%; top:auto; margin-left: -90px}
.clearlooks2 .mceConfirm .mceCancel {left:50%; top:auto}
.clearlooks2 .mceConfirm .mceIcon {background:url(img/confirm.gif)}
