# Custom Functions

Smarty comes with several custom plugin functions that you can use in
the templates.

- [{counter}](language-function-counter.md)
- [{cycle}](language-function-cycle.md)
- [{eval}](language-function-eval.md)
- [{fetch}](language-function-fetch.md)
- [{html_checkboxes}](language-function-html-checkboxes.md)
- [{html_image}](language-function-html-image.md)
- [{html_options}](language-function-html-options.md)
- [{html_radios}](language-function-html-radios.md)
- [{html_select_date}](language-function-html-select-date.md)
- [{html_select_time}](language-function-html-select-time.md)
- [{html_table}](language-function-html-table.md)
- [{mailto}](language-function-mailto.md)
- [{math}](language-function-math.md)
- [{textformat}](language-function-textformat.md)
