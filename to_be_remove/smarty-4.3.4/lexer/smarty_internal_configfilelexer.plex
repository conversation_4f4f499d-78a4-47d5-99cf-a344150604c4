<?php
/**
* Smarty Internal Plugin Configfilelexer
*
* This is the lexer to break the config file source into tokens 
* @package Smarty
* @subpackage Config
* <AUTHOR> Tews 
*/
/**
* Smarty_Internal_Configfilelexer
*
* This is the config file lexer.
* It is generated from the smarty_internal_configfilelexer.plex file
*
* @package Smarty
* @subpackage Compiler
* <AUTHOR> Tews
*/
class Smarty_Internal_Configfilelexer
{
    /**
     * Source
     *
     * @var string
     */
    public $data;
      /**
      * Source length
      *
      * @var int
      */
     public $dataLength = null;
   /**
     * byte counter
     *
     * @var int
     */
    public $counter;
    /**
     * token number
     *
     * @var int
     */
    public $token;
    /**
     * token value
     *
     * @var string
     */
    public $value;
    /**
     * current line
     *
     * @var int
     */
    public $line;
    /**
     * state number
     *
     * @var int
     */
    public $state = 1;
    /**
     * Smarty object
     *
     * @var Smarty
     */
    public $smarty = null;
    /**
     * compiler object
     *
     * @var Smarty_Internal_Config_File_Compiler
     */
    private $compiler = null;
    /**
     * copy of config_booleanize
     *
     * @var bool
     */
    private $configBooleanize = false;
    /**
     * trace file
     *
     * @var resource
     */
    public $yyTraceFILE;
    /**
     * trace prompt
     *
     * @var string
     */
    public $yyTracePrompt;
    /**
     * state names
     *
     * @var array
     */
    public $state_name = array(1 => 'START', 2 => 'VALUE', 3 => 'NAKED_STRING_VALUE', 4 => 'COMMENT', 5 => 'SECTION', 6 => 'TRIPPLE');

    /**
     * storage for assembled token patterns
     *
     * @var string
     */
    private $yy_global_pattern1 = null;
    private $yy_global_pattern2 = null;
    private $yy_global_pattern3 = null;
    private $yy_global_pattern4 = null;
    private $yy_global_pattern5 = null;
    private $yy_global_pattern6 = null;

    /**
     * token names
     *
     * @var array
     */
    public $smarty_token_names = array(        // Text for parser error messages
    );

    /**
     * constructor
     *
     * @param   string                             $data template source
     * @param Smarty_Internal_Config_File_Compiler $compiler
     */
    public function __construct($data, Smarty_Internal_Config_File_Compiler $compiler)
    {
        $this->data = $data . "\n"; //now all lines are \n-terminated
        $this->dataLength = strlen($data);
        $this->counter = 0;
        if (preg_match('/^\xEF\xBB\xBF/', $this->data, $match)) {
            $this->counter += strlen($match[0]);
        }
        $this->line = 1;
        $this->compiler = $compiler;
        $this->smarty = $compiler->smarty;
        $this->configBooleanize = $this->smarty->config_booleanize;
    }

   public function replace ($input) {
        return $input;
   }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }


/*!lex2php
%input $this->data
%counter $this->counter
%token $this->token
%value $this->value
%line $this->line
commentstart = /#|;/
openB = /\[/
closeB = /\]/
section = /.*?(?=[\.=\[\]\r\n])/
equal = /=/
whitespace = /[ \t\r]+/
dot = /\./
id = /[0-9]*[a-zA-Z_]\w*/
newline = /\n/
single_quoted_string = /'[^'\\]*(?:\\.[^'\\]*)*'(?=[ \t\r]*[\n#;])/
double_quoted_string = /"[^"\\]*(?:\\.[^"\\]*)*"(?=[ \t\r]*[\n#;])/
tripple_quotes = /"""/
tripple_quotes_end = /"""(?=[ \t\r]*[\n#;])/
text = /[\S\s]/
float = /\d+\.\d+(?=[ \t\r]*[\n#;])/
int = /\d+(?=[ \t\r]*[\n#;])/
maybe_bool = /[a-zA-Z]+(?=[ \t\r]*[\n#;])/
naked_string = /[^\n]+?(?=[ \t\r]*\n)/
*/

/*!lex2php
%statename START

commentstart {
    $this->token = Smarty_Internal_Configfileparser::TPC_COMMENTSTART;
    $this->yypushstate(self::COMMENT);
}
openB {
    $this->token = Smarty_Internal_Configfileparser::TPC_OPENB;
    $this->yypushstate(self::SECTION);
}
closeB {
    $this->token = Smarty_Internal_Configfileparser::TPC_CLOSEB;
}
equal {
    $this->token = Smarty_Internal_Configfileparser::TPC_EQUAL;
    $this->yypushstate(self::VALUE);
}
whitespace {
    return false;
}
newline {
    $this->token = Smarty_Internal_Configfileparser::TPC_NEWLINE;
}
id {
    $this->token = Smarty_Internal_Configfileparser::TPC_ID;
}
text {
    $this->token = Smarty_Internal_Configfileparser::TPC_OTHER;
}

*/

/*!lex2php
%statename VALUE

whitespace {
    return false;
}
float {
    $this->token = Smarty_Internal_Configfileparser::TPC_FLOAT;
    $this->yypopstate();
}
int {
    $this->token = Smarty_Internal_Configfileparser::TPC_INT;
    $this->yypopstate();
}
tripple_quotes {
    $this->token = Smarty_Internal_Configfileparser::TPC_TRIPPLE_QUOTES;
    $this->yypushstate(self::TRIPPLE);
}
single_quoted_string {
    $this->token = Smarty_Internal_Configfileparser::TPC_SINGLE_QUOTED_STRING;
    $this->yypopstate();
}
double_quoted_string {
    $this->token = Smarty_Internal_Configfileparser::TPC_DOUBLE_QUOTED_STRING;
    $this->yypopstate();
}
maybe_bool {
    if (!$this->configBooleanize || !in_array(strtolower($this->value), array('true', 'false', 'on', 'off', 'yes', 'no')) ) {
        $this->yypopstate();
        $this->yypushstate(self::NAKED_STRING_VALUE);
        return true; //reprocess in new state
    } else {
        $this->token = Smarty_Internal_Configfileparser::TPC_BOOL;
        $this->yypopstate();
    }
}
naked_string {
    $this->token = Smarty_Internal_Configfileparser::TPC_NAKED_STRING;
    $this->yypopstate();
}
newline {
    $this->token = Smarty_Internal_Configfileparser::TPC_NAKED_STRING;
    $this->value = '';
    $this->yypopstate();
}

*/

/*!lex2php
%statename NAKED_STRING_VALUE

naked_string {
    $this->token = Smarty_Internal_Configfileparser::TPC_NAKED_STRING;
    $this->yypopstate();
}

*/

/*!lex2php
%statename COMMENT

whitespace {
    return false;
}
naked_string {
    $this->token = Smarty_Internal_Configfileparser::TPC_NAKED_STRING;
}
newline {
    $this->token = Smarty_Internal_Configfileparser::TPC_NEWLINE;
    $this->yypopstate();
}

*/

/*!lex2php
%statename SECTION

dot {
    $this->token = Smarty_Internal_Configfileparser::TPC_DOT;
}
section {
    $this->token = Smarty_Internal_Configfileparser::TPC_SECTION;
    $this->yypopstate();
}

*/
/*!lex2php
%statename TRIPPLE

tripple_quotes_end {
    $this->token = Smarty_Internal_Configfileparser::TPC_TRIPPLE_QUOTES_END;
    $this->yypopstate();
    $this->yypushstate(self::START);
}
text {
  $to = strlen($this->data);
  preg_match("/\"\"\"[ \t\r]*[\n#;]/",$this->data,$match,PREG_OFFSET_CAPTURE,$this->counter);
  if (isset($match[0][1])) {
    $to = $match[0][1];
  } else {
    $this->compiler->trigger_config_file_error ('missing or misspelled literal closing tag');
  }  
  $this->value = substr($this->data,$this->counter,$to-$this->counter);
  $this->token = Smarty_Internal_Configfileparser::TPC_TRIPPLE_TEXT;
}
*/

}
