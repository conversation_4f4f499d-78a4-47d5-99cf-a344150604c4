#!/bin/bash

# 建立一個臨時檔案來存儲未被引用的檔案
TEMP_FILE=$(mktemp)
UNREFERENCED_FILES=$(mktemp)

# 獲取所有PHP檔案（排除to_be_remove和backup_files目錄）
find /Users/<USER>/Documents/GitHub/Pilotrun_test_Server -maxdepth 1 -name "*.php" -type f | grep -v "to_be_remove\|backup_files" > $TEMP_FILE

# 檢查每個檔案是否被引用
while IFS= read -r file; do
  filename=$(basename "$file")
  
  # 檢查是否為系統核心檔案，這些檔案通常是必要的
  if [[ "$filename" == "index.php" || "$filename" == "login.php" || "$filename" == "checkLogin.php" || "$filename" == "register.php" ]]; then
    echo "跳過核心檔案: $filename"
    continue
  fi
  
  # 在整個代碼庫中搜尋檔案名稱（排除檔案本身和to_be_remove目錄）
  # 使用更精確的搜尋模式，避免部分匹配
  references=$(grep -r --include="*.php" --include="*.tpl" --include="*.html" -E "(require|include|require_once|include_once|['\\\"]$filename['\\\"])" /Users/<USER>/Documents/GitHub/Pilotrun_test_Server | grep -v "$file\|to_be_remove\|backup_files" | wc -l)
  
  echo "檢查檔案: $filename, 引用數: $references"
  
  # 如果沒有引用，則添加到未引用檔案清單
  if [ "$references" -eq 0 ]; then
    echo "$file" >> $UNREFERENCED_FILES
  fi
done < $TEMP_FILE

# 輸出未被引用的檔案
echo "未被引用的檔案清單："
cat $UNREFERENCED_FILES

# 清理臨時檔案
rm $TEMP_FILE

# 保留未引用檔案清單供後續使用
echo "未引用檔案清單已保存到 $UNREFERENCED_FILES"
