# Third-Party Libraries Inventory - PilotRun Codebase

*Generated: 2025-07-16*  
*Last Updated: 2025-07-16*  
*Status: Post-Security Upgrade (Phase 1-2 Complete)*

This document provides a comprehensive inventory of all third-party libraries and frameworks used in the PilotRun Location-Based Service (LBS) platform.

## Table of Contents

1. [JavaScript Libraries](#javascript-libraries)
2. [PHP Libraries](#php-libraries)
3. [CSS Frameworks](#css-frameworks)
4. [Map Service Libraries](#map-service-libraries)
5. [Editor Libraries](#editor-libraries)
6. [Security & Maintenance Recommendations](#security--maintenance-recommendations)

---

## JavaScript Libraries

### Core JavaScript Framework

#### jQuery v3.7.1 ✅ **UPGRADED**
- **Location**: `/js/jquery-ui-1.13.3/external/jquery/jquery.js`
- **Size**: 287 KB
- **Purpose**: DOM manipulation, AJAX, event handling
- **Usage Status**: **Actively Used** - Core dependency for UI components
- **Dependencies**: None
- **Usage Examples**: 
  - `templates/Chart.tpl` (line 8)
  - `templates/BarChart.tpl` (line 209)
  - All template files using jQuery UI
- **Security Note**: ✅ **CURRENT** - Latest stable version, all known vulnerabilities patched
- **Upgrade Date**: 2025-07-16

#### jQuery UI v1.13.3 ✅ **UPGRADED**
- **Location**: `/js/jquery-ui-1.13.3/`
- **Size**: 1.4 MB (complete package)
- **Purpose**: UI widgets, interactions, effects
- **Usage Status**: **Actively Used** - Provides date pickers, dialogs, draggable elements
- **Dependencies**: jQuery 3.7.1
- **Components Used**:
  - Date picker widgets (extensively used in `MissionsManageRonnie.tpl`)
  - Dialog boxes for mission management
  - UI themes and styling
  - Draggable elements for interactive components
- **Security Note**: ✅ **CURRENT** - Latest stable version, compatible with jQuery 3.7.1
- **Upgrade Date**: 2025-07-16

### Chart and Visualization Libraries

#### Chart.js v4.5.0 ✅ **MIGRATED TO LOCAL**
- **Location**: `/js/chart.js/chart.umd.js` (Local)
- **Size**: 203 KB (UMD version)
- **Purpose**: Modern chart generation (replacing jQuery Flot)
- **Usage Status**: **Actively Used** - Primary charting solution
- **Dependencies**: None (standalone)
- **Additional Components**:
  - Date adapter: `/js/chart.js/chartjs-adapter-date-fns.bundle.min.js` (49 KB)
- **Usage Examples**: 
  - `templates/Chart.tpl` (time-series charts)
  - `templates/BarChart.tpl` (bar/pie charts)
- **Security Note**: ✅ **CURRENT** - Latest stable version, local hosting eliminates CDN dependencies
- **Migration Date**: 2025-07-16

#### jQuery Flot v0.8.3
- **Location**: `/js/flot/`
- **Size**: 488 KB (complete package)
- **Purpose**: Legacy charting library
- **Usage Status**: **Being Phased Out** - Replaced by Chart.js
- **Dependencies**: jQuery
- **Components**:
  - Core plotting: `jquery.flot.js` (123 KB)
  - Plugins: pie, time, canvas, selection, etc.
- **Security Note**: ⚠️ **LEGACY** - No longer maintained

#### WordCloud2.js
- **Location**: `/js/wordcloud2.js`
- **Size**: 391 KB
- **Purpose**: Word cloud visualization
- **Usage Status**: **Conditionally Used** - For specific visualization features
- **Dependencies**: HTML5 Canvas
- **Security Note**: ⚠️ **Unknown Version** - Version not clearly identified

### Form and Input Libraries

#### My97DatePicker v4.7
- **Location**: `/js/My97DatePicker/`
- **Size**: ~200 KB
- **Purpose**: Chinese-language date picker
- **Usage Status**: **Actively Used** - Primary date selection component
- **Dependencies**: None
- **Languages Supported**: English, Chinese (Simplified/Traditional)
- **Security Note**: ⚠️ **OUTDATED** - Last updated ~2012

#### LiveValidation
- **Location**: `/js/livevalidation/`
- **Size**: ~50 KB
- **Purpose**: Real-time form validation
- **Usage Status**: **Actively Used** - Form validation across the platform
- **Dependencies**: None
- **Security Note**: ⚠️ **LEGACY** - No longer maintained

### Media and UI Enhancement

#### ColorBox
- **Location**: `/js/jquery.colorbox.js`, `/js/jquery.colorbox-min.js`
- **Size**: ~30 KB
- **Purpose**: Lightbox for images and media
- **Usage Status**: **Actively Used** - Image galleries and popups
- **Dependencies**: jQuery
- **Security Note**: ⚠️ **UNKNOWN VERSION**

---

## PHP Libraries

### Template Engine

#### Smarty v4.3.4 ✅ **DIRECTORY RENAMED**
- **Location**: `/libs/smarty4/` (renamed from `/libs/smarty3/`)
- **Size**: ~2 MB
- **Purpose**: PHP template engine
- **Usage Status**: **Actively Used** - Primary templating system
- **Version**: 4.3.4 (Latest stable)
- **Dependencies**: PHP 7.4+ or 8.x
- **Configuration**: `/inc/smarty_init.php` (updated path references)
- **Features Used**:
  - Template compilation
  - Variable assignment
  - Custom modifiers
  - Debugging capabilities
- **Security Note**: ✅ **CURRENT** - Modern version with corrected directory naming
- **Path Update Date**: 2025-07-16
- **Note**: Directory name now reflects actual version (was misleadingly named smarty3)

#### Legacy Smarty 2.6.19 (Deprecated)
- **Location**: `/libs/` (internals/, plugins/)
- **Size**: ~1 MB
- **Purpose**: Legacy template engine files
- **Usage Status**: **Deprecated** - Kept for compatibility during transition
- **Security Note**: ❌ **DEPRECATED** - Should be removed after migration completion

### Composer Dependencies

#### From composer.json:
```json
{
    "require": {
        "php": "^7.4 || ^8.0",
        "smarty/smarty": "^4.3"
    }
}
```

---

## Editor Libraries

### Rich Text Editors

#### TinyMCE v3.4.5 ❌ **REMOVED**
- **Previous Location**: `/js/tiny_mce/` (moved to `/to_be_remove/`)
- **Size**: 2.4 MB
- **Purpose**: WYSIWYG rich text editor
- **Usage Status**: ❌ **REMOVED** - Analysis confirmed it was unused in the codebase
- **Replacement**: UEditor Mini (already in use for Chinese content)
- **Security Note**: ✅ **SECURITY RISK ELIMINATED** - Removed unused vulnerable component
- **Removal Date**: 2025-07-16
- **Analysis**: Confirmed no active usage in any PHP or template files

#### UEditor Mini
- **Location**: `/js/ueditor_mini/`
- **Size**: 3.7 MB (including uploads)
- **Purpose**: Lightweight Chinese-language editor
- **Usage Status**: **Conditionally Used** - Chinese content editing
- **Dependencies**: jQuery 1.10.2
- **Languages**: Chinese, English
- **Security Note**: ⚠️ **UNKNOWN MAINTENANCE STATUS**

---

## Map Service Libraries

### Leaflet v1.7.1
- **Location**: `/js/leaflet@1.7.1/dist/`
- **Size**: ~150 KB
- **Purpose**: Modern interactive maps
- **Usage Status**: **Actively Used** - Primary mapping solution
- **Dependencies**: None
- **Features**: Markers, tiles, interactions
- **Security Note**: ✅ **GOOD** - Well-maintained, but not latest version

### Custom Map Libraries
- **PRCoords.js**: Coordinate conversion utilities
- **GPSConvert.js**: GPS coordinate transformations
- **geolet.js**: Geolocation utilities ❌ **REMOVED** (moved to to_be_remove/)

### External Map Services (API Integration)
- **Baidu Maps**: Chinese market mapping
- **Gaode Maps**: Chinese market mapping  
- **Google Maps**: International mapping
- **OpenStreetMap**: Open-source mapping

---

## CSS Frameworks

#### jQuery UI CSS v1.13.3 ✅ **UPGRADED**
- **Location**: `/js/jquery-ui-1.13.3/jquery-ui.css`
- **Size**: ~35 KB
- **Purpose**: UI component styling
- **Usage Status**: **Actively Used**
- **Dependencies**: jQuery UI 1.13.3 JavaScript
- **Upgrade Date**: 2025-07-16

#### ColorBox CSS
- **Location**: `/css/colorbox.css`
- **Purpose**: Lightbox styling
- **Usage Status**: **Actively Used**

#### Custom CSS
- **Location**: `/css/`
- **Files**: `css-min.css`, `newstyle.css`, `tooltip.css`, `lightbox.css`
- **Purpose**: Custom platform styling

---

## Security & Maintenance Recommendations

### ✅ **CRITICAL ISSUES RESOLVED**

1. **jQuery 1.10.2** ✅ **RESOLVED**
   - **Previous Risk**: Multiple known XSS vulnerabilities
   - **Action Taken**: Upgraded to jQuery 3.7.1 (2025-07-16)
   - **Status**: All vulnerabilities patched, modern ES6+ support added

2. **TinyMCE 3.4.5** ✅ **RESOLVED**
   - **Previous Risk**: Critical security vulnerabilities, XSS potential
   - **Action Taken**: Completely removed (unused component)
   - **Status**: Security risk eliminated, 2.4MB disk space recovered

3. **jQuery UI 1.11.4** ✅ **RESOLVED**
   - **Previous Risk**: Known security issues
   - **Action Taken**: Upgraded to jQuery UI 1.13.3 (2025-07-16)
   - **Status**: All security issues patched, enhanced compatibility

---

## 🗑 **Removed/Cleaned Libraries**

### Duplicate jQuery Versions ❌ **REMOVED**
- **jquery-1.3.2.min.js** (59 KB) - Ancient version
- **jquery-1.6.2.min.js** (91 KB) - Outdated version  
- **jquery-1.8.3.min.js** (93 KB) - Legacy version
- **jquery-1.9.1.min.js** (93 KB) - Replaced version
- **jquery-1.10.2.min.js** (91 KB) - Superseded by 3.7.1
- **jquery-2.1.1.min.js** (84 KB) - Legacy version
- **Status**: All moved to `to_be_remove/` (2025-07-16)
- **Space Saved**: ~511 KB

### Unused JavaScript Libraries ❌ **REMOVED**  
- **moo.fx.js** (15 KB) - Legacy animation library
- **prototype.lite.js** (15 KB) - Prototype framework subset
- **litebox-1.0.js** (8 KB) - Legacy lightbox
- **JSCookMenu/** (47 KB) - Legacy menu system
- **geolet.js** (5 KB) - Geolocation utilities
- **Status**: All moved to `to_be_remove/` (2025-07-16)
- **Space Saved**: ~90 KB

### Coordinate Libraries ❌ **REMOVED**
- **proj4.js** (64 KB) - Projection library (unused)
- **proj4leaflet.js** (24 KB) - Leaflet integration (unused)
- **Analysis**: No references found in codebase
- **Status**: Moved to `to_be_remove/` (2025-07-16)
- **Space Saved**: 88 KB

### Development Documentation ❌ **REMOVED**
- **js/flot/examples/** - Development examples
- **js/flot/API.md** - API documentation
- **Various README files** - Developer documentation
- **Status**: Moved to `to_be_remove/` (2025-07-16)
- **Space Saved**: ~500 KB

### Legacy Smarty Duplicate ❌ **REMOVED**
- **smarty-4.3.4/** - Duplicate Smarty installation
- **Size**: 2.1 MB
- **Status**: Moved to `to_be_remove/` (2025-07-16)
- **Note**: Duplicate of properly installed version in libs/smarty4/

### Total Cleanup Summary
- **Files Removed**: 25+ duplicate/unused files
- **Directories Cleaned**: 8 legacy directories
- **Total Space Saved**: ~3.2 MB
- **Security Improvements**: Removed potential attack surfaces

---

### Medium Priority Issues

4. **My97DatePicker** ⚠️
   - **Risk**: Unmaintained, potential compatibility issues
   - **Recommendation**: Replace with modern date picker (e.g., Flatpickr)

5. **LiveValidation** ⚠️
   - **Risk**: Unmaintained library
   - **Recommendation**: Replace with modern validation library

6. **jQuery Flot** ✅ **MIGRATION COMPLETED**
   - **Previous Risk**: No longer maintained
   - **Action Taken**: Completely replaced with Chart.js 4.5.0
   - **Status**: Migration completed, legacy Flot code removed from templates

### Low Priority Issues

7. **WordCloud2.js** ℹ️
   - **Recommendation**: Verify version and update if needed

8. **ColorBox** ℹ️
   - **Recommendation**: Consider modern lightbox alternatives

### Positive Notes

✅ **Smarty 4.3** - Recently upgraded, modern and secure  
✅ **Chart.js** - Modern replacement strategy in progress  
✅ **Leaflet 1.7.1** - Good choice for mapping, consider updating to latest  

### ✅ **MIGRATION STATUS**

#### ✅ Phase 1 (Critical - Immediate) - **COMPLETED**
1. ✅ **Upgrade jQuery to 3.7.1** - Completed 2025-07-16
2. ✅ **Remove TinyMCE** - Completed 2025-07-16 (unused, removed entirely)
3. ✅ **Update jQuery UI to 1.13.3** - Completed 2025-07-16

#### ✅ Phase 2 (Medium Priority) - **PARTIALLY COMPLETED**
1. ⏳ Replace My97DatePicker with modern alternative - **Pending**
2. ⏳ Replace LiveValidation with modern validation - **Pending**
3. ✅ **Complete Chart.js migration** - Completed 2025-07-16

#### ⏳ Phase 3 (Enhancement) - **PENDING**
1. ⏳ Update Leaflet to latest version
2. ⏳ Consider replacing older UI libraries
3. ⏳ Implement modern build process for dependencies

#### 🎯 **NEXT PRIORITIES**
1. Replace My97DatePicker (security & maintenance)
2. Replace LiveValidation (modernization)
3. Update Leaflet to latest version
4. Implement npm-based dependency management

### Dependency Management Recommendations

1. **Implement npm/yarn** for JavaScript dependency management
2. **Use CDN with integrity checks** for external libraries
3. **Regular security audits** using tools like `npm audit`
4. **Document all library versions** in package.json/composer.json
5. **Establish update schedule** for security patches

---

## File Size Summary

| Category | Total Size | Count | Status |
|----------|------------|-------|--------|
| JavaScript Libraries | ~6.7 MB | 40+ files | ✅ **Reduced by 2.4MB** |
| PHP Libraries (Smarty) | ~3 MB | 100+ files | ✅ **Organized** |
| CSS Frameworks | ~110 KB | 10+ files | ✅ **Updated** |
| **Total** | **~9.8 MB** | **150+ files** | ✅ **20% Reduction** |

### 📊 **Cleanup Summary**
- **Removed**: 2.4MB (TinyMCE unused)
- **Upgraded**: jQuery, jQuery UI, Chart.js
- **Security Issues**: 3 critical issues resolved
- **Disk Space Saved**: ~2.4MB
- **Files Cleaned**: 10+ legacy/duplicate files moved to `to_be_remove/`

---

*This inventory should be reviewed and updated quarterly to maintain security and performance standards.*

---

## 🎯 **Recent Upgrade Summary (2025-07-16)**

### ✅ **Completed Upgrades**
1. **jQuery**: 1.10.2 → 3.7.1 (Major security upgrade)
2. **jQuery UI**: 1.11.4 → 1.13.3 (Security & compatibility upgrade)
3. **Chart.js**: CDN → Local 4.5.0 (Performance & reliability)
4. **TinyMCE**: Removed (Unused, 2.4MB saved)
5. **Smarty**: Directory structure corrected (smarty3 → smarty4)

### 🔒 **Security Improvements**
- ✅ 3 critical vulnerabilities resolved
- ✅ All known XSS vulnerabilities in jQuery patched
- ✅ Removed unused attack surface (TinyMCE)
- ✅ Modern HTTPS CDN dependencies eliminated

### 📈 **Performance Improvements**
- ✅ Local Chart.js hosting (eliminates CDN dependency)
- ✅ Modern jQuery performance optimizations
- ✅ Reduced total library footprint by 20%
- ✅ Enhanced browser compatibility

### 🛠 **Technical Debt Reduction**
- ✅ Eliminated version conflicts (mixed jQuery versions)
- ✅ Corrected misleading directory names
- ✅ Removed legacy/duplicate files
- ✅ Updated all template references

### 📋 **Testing Status**
- ✅ Chart functionality verified
- ✅ jQuery UI components tested (dialogs, date pickers)
- ✅ Template compatibility confirmed
- ⏳ Comprehensive integration testing recommended