{* GameManage.tpl *}
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>派樂-遊戲整合系統</title>

    <link type="text/css" rel="stylesheet" href="css/css-min.css">
    <link type="text/css" rel="stylesheet" href="css/tooltip.css">
    <script type="text/javascript" src="js/jquery-ui-1.13.3/external/jquery/jquery.js"></script>
    <script type="text/javascript" src="js/jquery-ui-1.13.3/jquery-ui.js"></script>
    <script type="text/javascript" src="js/livevalidation/livevalidation_standalone.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?v=weekly&key=AIzaSyDMBUPoQVrXMOXv55bETHQyDgtVy-0C4Os&loading=async&callback=initMap" async defer></script>
    <link type="text/css" rel="stylesheet" href="js/jquery-ui-1.13.3/jquery-ui.css">
    <link type="text/css" rel="stylesheet" href="js/livevalidation/css/consolidated_common.css">

    {* Wrap ALL JavaScript logic inside {literal} *}
    {literal}
    <script>
    // 調試標記 - 地圖選擇修復版本 v1.1 - 2025-07-16
    console.log('GameManage.tpl 地圖選擇修復版本 v1.1 已加載');

    var map, rectangle; // Global vars for Google Maps

    $(function() {
        // 初始化時清除錯誤訊息
        $("#f2msg").text('');
 
        // 初始化 datepicker
        $("#g_end_date").datepicker();
        $("#g_begin_date").datepicker();

        // 日期與時間欄位即時驗證
        $("#g_begin_date, #g_begin_time, #g_end_date, #g_end_time").on('change input blur', function() {
            validateGameDates();
        });

        // 監聽 F2 輸入變化
        $("#g_f2_psw").on('input', function() {
            if ($(this).val().length > 0) {
                GetF2();
            } else {
                $("#f2msg").text('');
            }
        });
 
        // Initialize map if the canvas exists
        if ($('#map-canvas').length) {
             // Google Maps API callback 'initMap' will call initialize
        }
        
        // 初始化背景主題功能
        updateThemePreview();
        toggleBackgroundOptions();
    });

    function GetF2() {
        // Read vars from data attributes
        var formElement = $('#form1');
        var f2InputError = formElement.data('f2-input-error');
        var f2ReservedError = formElement.data('f2-reserved-error');
        var f2DuplicateError = formElement.data('f2-duplicate-error');
        var userName = formElement.data('user-name');
        var gId = $("#g_id").val(); // Read g_id from hidden input

        // Use RegExp literal (should be safe inside literal)
        var checkNum = /^[a-zA-Z]{2,3}$/;
        if (!checkNum.test($("#g_f2_psw").val())) {
            $("#f2msg").css("display", "block").text(f2InputError);
            console.log('Showing error:', f2InputError);
            return false;
        }
        if (userName != 'Trial' && $("#g_f2_psw").val().toLowerCase().substr(0,1) == 't') {
            $("#f2msg").css("display", "block").text(f2ReservedError);
            console.log('Showing error:', f2ReservedError);
            return false;
        }
        $("#f2msg").text("").css("display", "none");
        var flag = false;
        $.ajaxSetup({ async: false });
        $.get("API.php", {action: "SeachF2", f2: $("#g_f2_psw").val(), g_id: gId}, function(data) {
            if (data == '1') {
                flag = true;
            } else {
                $("#f2msg").css("display", "block").text(f2DuplicateError);
                console.log('Showing error:', f2DuplicateError);
                flag = false;
            }
        });
        return flag;
    }

    function isNumberKey(evt) {
        var charCode = (evt.which) ? evt.which : event.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) return false;
        return true;
    }

    function changeDisable() {
        var rankCheckbox = document.getElementById('g_rank_limit_checkbox').checked;
        var rankInput = document.getElementById('g_rank_limit');
        if (!rankCheckbox) {
            rankInput.setAttribute("disabled", "disabled");
            rankInput.value = "-1";
        } else {
            rankInput.removeAttribute("disabled");
            rankInput.value = rankInput.value === "-1" ? "1" : rankInput.value;
        }
    }

    function isValidGroups(ev) {
        var bValid = (47 < ev.keyCode && ev.keyCode < 58) || ev.keyCode == 13;
        var spanid = "#checkMsg";
        if (!bValid) {
            // Read from data attribute
            var creditsStr11 = $('#form1').data('credits-str-11');
            $(spanid).text(creditsStr11 + " 3,8,10...");
            $(spanid).css("visibility", "visible");
        } else {
            $(spanid).css("visibility", "hidden");
        }
        return bValid;
    }

    // 驗證遊戲日期時間
    function validateGameDates() {
        var formElement = $('#form1');
        var timeFormatError = formElement.data('time-format-error');
        var endDateBeforeStartDateError = formElement.data('end-date-before-start-date-error');
        var durationExceeds24HoursError = formElement.data('duration-exceeds-24-hours-error');
        var userState = formElement.data('user-state');
        var isAdmin = (userState == 1 || userState == "1" || userState == 4 || userState == "4");
        console.log("userState=", userState, "isAdmin=", isAdmin);

        // 管理員完全不做任何時間驗證
        if (isAdmin) {
            $("#gbtmsg").css("visibility", "hidden").text("");
            return true;
        }

        var beginDateStr = $("#g_begin_date").val();
        var beginTimeStr = $("#g_begin_time").val();
        var endDateStr = $("#g_end_date").val();
        var endTimeStr = $("#g_end_time").val();
        var gbtmsg = $("#gbtmsg");

        // 檢查日期是否為空
        if (!beginDateStr || !endDateStr) {
            gbtmsg.css("visibility", "visible").text("開始和結束日期不能為空");
            return false;
        }

        // 檢查時間格式
        var checkTimeFormat = /^(20|21|22|23|[0-1]?\d):([0-5]?\d):([0-5]?\d)$/;
        if (!checkTimeFormat.test(beginTimeStr) || !checkTimeFormat.test(endTimeStr)) {
            gbtmsg.css("visibility", "visible").text(timeFormatError);
            return false;
        }

        // 計算時間戳記
        var beginTimestamp = new Date(beginDateStr + " " + beginTimeStr).getTime();
        var endTimestamp = new Date(endDateStr + " " + endTimeStr).getTime();

        // 檢查結束日期是否早於開始日期
        if (endTimestamp < beginTimestamp) {
            gbtmsg.css("visibility", "visible").text(endDateBeforeStartDateError);
            return false;
        }

        // 檢查時間長度是否超過 24 小時
        var durationHours = (endTimestamp - beginTimestamp) / (1000 * 60 * 60);
        if (durationHours > 24) {
            gbtmsg.css("visibility", "visible").text(durationExceeds24HoursError);
            return false;
        }

        gbtmsg.css("visibility", "hidden").text("");
        return true;
    }

    // 檢查日期並防止自動修改
    function CheckBeginDate() {
        return validateGameDates();
    }

    function onGps() {
        if ($('#g_gps').is(":checked")) rectangle.setVisible(true);
        else rectangle.setVisible(false);
    }

    // initMap is called by Google Maps API callback
    // Make it global by attaching to window object
    window.initMap = function() {
        // Check if already initialized to prevent multiple loads
        if (typeof map !== 'undefined') {
            return;
        }
        window.addEventListener('load', initialize);
        // If window is already loaded, call initialize directly
        if (document.readyState === 'complete') {
            initialize();
        }
    }

    // initialize reads g_bounds from data attribute
    function initialize() {
        var formElement = $('#form1');
        var strBounds = formElement.data('g-bounds') || ''; // Read from data attribute

        // Use string split/join for replacement
        strBounds = strBounds.split('(').join('');
        strBounds = strBounds.split(')').join('');
        strBounds = strBounds.split(' ').join('');
        strBounds = strBounds.trim();

        var arrBounds = strBounds.split(";");
        var strBoundSW = $.trim(arrBounds[0] || "24.0697,121.3008");
        var strBoundNE = $.trim(arrBounds[1] || "24.3715,121.6935");
        var arrBoundSW = strBoundSW.split(",");
        var arrBoundNE = strBoundNE.split(",");
        var bVisible = arrBoundSW.length == 2 && arrBoundNE.length == 2;
        var bounds = new google.maps.LatLngBounds(
            new google.maps.LatLng($.trim(arrBoundSW[0]), $.trim(arrBoundSW[1])),
            new google.maps.LatLng($.trim(arrBoundNE[0]), $.trim(arrBoundNE[1]))
        );
        $("#g_bounds").val("(" + arrBoundSW[0] + "," + arrBoundSW[1] + ");(" + arrBoundNE[0] + "," + arrBoundNE[1] + ")");
        rectangle = new google.maps.Rectangle({
            bounds: bounds,
            editable: true,
            draggable: true
        });
        rectangle.setVisible(bVisible);
        $('#g_gps').attr("checked", bVisible);
        var mapOptions = { center: rectangle.getBounds().getCenter(), zoom: 9 };
        map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
        rectangle.setMap(map);
        google.maps.event.addListener(rectangle, 'bounds_changed', function() {
            var bnds = rectangle.getBounds();
            $("#g_bounds").val(bnds.getSouthWest().toString() + ";" + bnds.getNorthEast().toString());
        });
    }

    function changeBeginDate() {
        document.getElementById('g_end_date').value = document.getElementById('g_begin_date').value;
    }

    // 添加 readUrl 函數用於處理文件上傳預覽
    function readUrl(input) {
        console.log("readUrl 函數被調用", input.id);
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                if (input.id === 'uploadinput') {
                    $('#game-icon').attr('src', e.target.result);
                    $('#upload-detail').html(input.files[0].name + ' (' + Math.round(input.files[0].size / 1024) + 'KB)');
                } else if (input.id === 'uploadinputlogo') {
                    $('#game-logo').attr('src', e.target.result);
                    $('#upload-detail-logo').html(input.files[0].name + ' (' + Math.round(input.files[0].size / 1024) + 'KB)');
                }
            }
            reader.readAsDataURL(input.files[0]);
        }
    }
    
    // 背景主題相關功能
    function updateThemePreview() {
        var themeType = $('#theme_type').val();
        var previewElement = $('#theme-preview');
        
        var themeGradients = {
            'original': 'linear-gradient(135deg, #FAFAFA, #FAFAFA)',
            'default': 'linear-gradient(135deg, #F5F7FA, #C3CFE2)', 
            'marvel': 'linear-gradient(135deg, #ED213A, #93291E)',
            'harry_potter': 'linear-gradient(135deg, #EECDA3, #EF629F)',
            'adventure': 'linear-gradient(135deg, #16A085, #F4D03F)',
            'mystery': 'linear-gradient(135deg, #667eea, #764ba2)',
            'ocean': 'linear-gradient(135deg, #2E3192, #1BFFFF)'
        };
        
        previewElement.css('background', themeGradients[themeType] || themeGradients['original']);
        console.log('主題預覽更新:', themeType);
    }
    
    function toggleBackgroundOptions() {
        var isActive = $('#bg_status_active').is(':checked');
        var section = $('#background-image-section');
        
        if (isActive) {
            section.css('opacity', '1');
            section.find('input, button, a').prop('disabled', false);
        } else {
            section.css('opacity', '0.5');
            section.find('input, button, a').prop('disabled', true);
        }
        console.log('背景選項切換:', isActive ? '啟用' : '停用');
    }
    
    function previewBackgroundImage(input) {
        console.log('背景圖片預覽被觸發');
        if (input.files && input.files[0]) {
            var file = input.files[0];
            var maxSize = 2 * 1024 * 1024; // 2MB
            
            // 檢查檔案大小
            if (file.size > maxSize) {
                alert('檔案大小超過 2MB 限制，請選擇較小的圖片');
                input.value = '';
                return;
            }
            
            // 檢查檔案類型
            var allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('不支援的檔案格式，請選擇 JPG, PNG 或 WebP 格式');
                input.value = '';
                return;
            }
            
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#bg-preview').attr('src', e.target.result);
                $('#bg-file-info').text(file.name + ' (' + Math.round(file.size / 1024) + 'KB)');
                console.log('背景圖片預覽成功:', file.name);
            };
            reader.readAsDataURL(file);
        }
    }
    
    // 原始 submitData 函數已移動到向導系統中統一處理
        
        // ==================== 向導系統 JavaScript ====================
        
        // 向導全局變量
        let currentWizardStep = 1;
        const totalSteps = 6;
        let formData = {};
        let validationErrors = {};
        let autoSaveEnabled = false;
        
        // 初始化向導
        function initializeWizard() {
            setupEventListeners();
            updateProgress();
            updatePreview();
            loadDraftData();
            // 不在初始化時啟動自動保存
            
        }
        
        
        function setupEventListeners() {
            console.log('設置事件監聽器');
            
            // 表單輸入事件
            $('#wizard_g_title').on('input', function() {
                updatePreview();
                validateField(this);
                autoSave();
            });
            
            $('#wizard_g_content').on('input', function() {
                updatePreview();
                validateField(this);
                autoSave();
            });
            
            $('#wizard_g_f2_psw').on('input', function() {
                validateField(this);
                // 即時檢查F2密碼重複性
                if ($(this).val().length > 0) {
                    checkWizardF2Duplicate($(this).val());
                }
                autoSave();
            });
            
            $('#wizard_g_team_count').on('input', function() {
                validateField(this);
                autoSave();
            });
            
            // 圖片上傳事件
            $('#wizard_uploadinput').on('change', function() {
                handleImageUpload(this);
            });
            
            // 時間日期變化事件 - 即時檢測
            $('#wizard_g_begin_date, #wizard_g_begin_time, #wizard_g_end_date, #wizard_g_end_time').on('change input blur', function() {
                validateTimeRange();
                autoSave();
            });
            
            // 地圖類型選擇事件
            $('#wizard_g_map_type').on('change', function() {
                const selectedValue = $(this).val();
                const selectedText = $(this).find('option:selected').text();

                console.log('=== 地圖類型選擇事件觸發 ===');
                console.log('選擇的值:', selectedValue);
                console.log('選擇的文字:', selectedText);
                console.log('事件時間:', new Date().toLocaleTimeString());

                validateField(this);

                // 標記用戶已手動選擇地圖類型，避免草稿恢復覆蓋
                $(this).data('user-selected', true);
                console.log('已設置用戶選擇標記為 true');

                autoSave();

                // 驗證選擇是否保持
                setTimeout(() => {
                    const currentValue = $(this).val();
                    const currentText = $(this).find('option:selected').text();
                    console.log('500ms後驗證 - 值:', currentValue, '文字:', currentText);

                    if (currentValue !== selectedValue) {
                        console.error('警告：地圖選擇被重置了！原值:', selectedValue, '現值:', currentValue);
                    }
                }, 500);
            });
            
            // 附加資訊輸入事件
            $('#wizard_g_add').on('input', function() {
                validateField(this);
                autoSave();
            });
            
            // 步驟導航點擊
            $('.nav-step').on('click', function() {
                const targetStep = parseInt($(this).data('step'));
                if (!$(this).hasClass('disabled')) {
                    goToStep(targetStep);
                }
            });
        }
        
        function changeStep(direction) {
            const newStep = currentWizardStep + direction;
            
            if (direction > 0) {
                if (validateCurrentStep()) {
                    goToStep(newStep);
                }
            } else {
                goToStep(newStep);
            }
        }
        
        function goToStep(step) {
            if (step < 1 || step > totalSteps) return;
            
            // 隱藏當前步驟
            $(`.step-content[data-step="${currentWizardStep}"]`).removeClass('active');
            $(`.nav-step[data-step="${currentWizardStep}"]`).removeClass('active');
            
            // 顯示目標步驟
            $(`.step-content[data-step="${step}"]`).addClass('active');
            $(`.nav-step[data-step="${step}"]`).addClass('active');
            
            // 更新步驟狀態
            if (step > currentWizardStep) {
                for (let i = currentWizardStep; i < step; i++) {
                    $(`.nav-step[data-step="${i}"]`).addClass('completed').removeClass('disabled');
                }
            }
            
            // 啟用可訪問的步驟
            for (let i = 1; i <= step + 1; i++) {
                $(`.nav-step[data-step="${i}"]`).removeClass('disabled');
            }
            
            currentWizardStep = step;
            updateNavigationButtons();
            updateProgress();
            
            // 當進入第二步時啟動自動保存功能
            if (step === 2) {
                setupAutoSave();
                console.log('進入第二步驟，檢查地圖選單:', $('#wizard_g_map_type').length);

                // 立即檢查地圖選單狀態，避免延遲導致的衝突
                const mapValue = $('#wizard_g_map_type').val();
                const mapText = $('#wizard_g_map_type option:selected').text();
                const userSelected = $('#wizard_g_map_type').data('user-selected');

                console.log('第二步驟地圖選單值:', mapValue);
                console.log('第二步驟地圖選單文字:', mapText);
                console.log('用戶是否已手動選擇:', userSelected);

                // 只有在用戶未手動選擇且地圖選單沒有值時，才從草稿恢復
                if (!userSelected && (!mapValue || mapValue === '')) {
                    const draftData = localStorage.getItem('gameManage_draft');
                    if (draftData) {
                        try {
                            const data = JSON.parse(draftData);
                            if (data.g_map_type && !data.userSelectedMap) {
                                console.log('從草稿恢復地圖類型:', data.g_map_type);
                                $('#wizard_g_map_type').val(data.g_map_type);

                                // 不觸發 change 事件，避免標記為用戶選擇
                                console.log('恢復後地圖選單值:', $('#wizard_g_map_type').val());
                                console.log('恢復後地圖選單文字:', $('#wizard_g_map_type option:selected').text());
                            } else if (data.userSelectedMap) {
                                console.log('草稿中標記為用戶選擇，恢復用戶選擇標記');
                                $('#wizard_g_map_type').data('user-selected', true);

                                // 如果草稿中有地圖類型值，也要恢復
                                if (data.g_map_type) {
                                    $('#wizard_g_map_type').val(data.g_map_type);
                                    console.log('恢復用戶之前選擇的地圖類型:', data.g_map_type);
                                }
                            }
                        } catch (e) {
                            console.error('草稿解析失敗:', e);
                        }
                    }
                }
            }
            
            // 添加動畫效果
            $(`.step-content[data-step="${step}"]`).addClass('fade-in');
            setTimeout(() => {
                $(`.step-content[data-step="${step}"]`).removeClass('fade-in');
            }, 500);
        }
        
        function updateNavigationButtons() {
            const $prevBtn = $('#prevBtn');
            const $nextBtn = $('#nextBtn');
            
            if (currentWizardStep === 1) {
                $prevBtn.prop('disabled', true);
            } else {
                $prevBtn.prop('disabled', false);
            }
            
            if (currentWizardStep === totalSteps) {
                $nextBtn.text('完成創建 ✓').removeClass('btn-primary').addClass('btn-success');
            } else {
                $nextBtn.text('下一步 →').removeClass('btn-success').addClass('btn-primary');
            }
            
            $('#currentStep').text(currentWizardStep);
        }
        
        function updateProgress() {
            const completedSteps = currentWizardStep - 1;
            const progressPercentage = Math.round((completedSteps / totalSteps) * 100);
            
            $('.progress-percentage').text(progressPercentage + '%');
            $('.progress-circle').css('--progress-angle', (progressPercentage / 100) * 360 + 'deg');
            
            const remainingTime = Math.max(0, (totalSteps - currentWizardStep) * 2);
            if (remainingTime > 0) {
                $('.progress-overview p').text(`預計還需 ${remainingTime}-${remainingTime + 5} 分鐘`);
            } else {
                $('.progress-overview p').text('即將完成！');
            }
        }
        
        function validateCurrentStep() {
            let isValid = true;
            
            if (currentWizardStep === 1) {
                isValid = validateField($('#wizard_g_title')[0]) && isValid;
                isValid = validateField($('#wizard_g_content')[0]) && isValid;
                isValid = validateField($('#wizard_g_f2_psw')[0]) && isValid;
                isValid = validateField($('#wizard_g_team_count')[0]) && isValid;
            } else if (currentWizardStep === 2) {
                isValid = validateField($('#wizard_g_begin_date')[0]) && isValid;
                isValid = validateField($('#wizard_g_begin_time')[0]) && isValid;
                isValid = validateField($('#wizard_g_end_date')[0]) && isValid;
                isValid = validateField($('#wizard_g_end_time')[0]) && isValid;
                isValid = validateTimeRange() && isValid;
                isValid = validateField($('#wizard_g_map_type')[0]) && isValid;
            }
            
            return isValid;
        }
        
        function validateField(field) {
            const $field = $(field);
            const $container = $field.closest('.enhanced-input');
            const fieldName = $field.attr('name') || $field.attr('id');
            const value = $field.val().trim();
            
            $container.removeClass('error success');
            
            let isValid = true;
            let errorMessage = '';
            
            if ($field.attr('required') || $container.find('label .required-indicator').length > 0) {
                if (!value) {
                    isValid = false;
                    errorMessage = '此欄位為必填項目';
                }
            }
            
            if (isValid && value) {
                switch (fieldName) {
                    case 'g_title':
                    case 'wizard_g_title':
                        if (value.length < 2) {
                            isValid = false;
                            errorMessage = '遊戲標題至少需要2個字符';
                        }
                        break;
                        
                    case 'g_content':
                    case 'wizard_g_content':
                        if (value.length < 10) {
                            isValid = false;
                            errorMessage = '遊戲描述至少需要10個字符';
                        }
                        break;
                        
                    case 'g_f2_psw':
                    case 'wizard_g_f2_psw':
                        // 格式驗證
                        if (!/^[a-zA-Z]{2,3}$/.test(value)) {
                            isValid = false;
                            errorMessage = '請輸入2-3碼英文字母（區分大小寫）';
                        } else {
                            // 保留字檢查
                            var formElement = $('#form1');
                            var userName = formElement.data('user-name');
                            if (userName !== 'Trial' && value.toLowerCase().substr(0,1) === 't') {
                                isValid = false;
                                errorMessage = '此密碼代碼為保留字，請更換其他密碼';
                            } else {
                                // 資料庫重複性檢查（同步）
                                var gId = $("#g_id").val() || '';
                                var isDuplicate = false;
                                
                                $.ajaxSetup({ async: false });
                                $.get("API.php", {action: "SeachF2", f2: value, g_id: gId}, function(data) {
                                    if (data !== '1') {
                                        isDuplicate = true;
                                    }
                                });
                                
                                if (isDuplicate) {
                                    isValid = false;
                                    errorMessage = '此密碼已被其他遊戲使用，請更換其他密碼';
                                }
                            }
                        }
                        break;
                        
                    case 'g_team_count':
                    case 'wizard_g_team_count':
                        const teamCount = parseInt(value);
                        if (isNaN(teamCount) || teamCount < 1 || teamCount > 999) {
                            isValid = false;
                            errorMessage = '小隊數量必須在1-999之間';
                        }
                        break;
                        
                    case 'g_map_type':
                    case 'wizard_g_map_type':
                        console.log('地圖類型驗證:', value);
                        if (!value || value === '') {
                            isValid = false;
                            errorMessage = '請選擇地圖類型';
                        }
                        break;
                }
            }
            
            if (isValid) {
                $container.addClass('success');
                validationErrors[fieldName] = null;
            } else {
                $container.addClass('error');
                $container.find('.field-error').text(errorMessage);
                validationErrors[fieldName] = errorMessage;
            }
            
            return isValid;
        }
        
        function validateTimeRange() {
            const beginDate = $('#wizard_g_begin_date').val();
            const beginTime = $('#wizard_g_begin_time').val();
            const endDate = $('#wizard_g_end_date').val();
            const endTime = $('#wizard_g_end_time').val();
            
            // 清除之前的錯誤訊息
            $('#gbtmsg').css('visibility', 'hidden').text('');
            
            if (!beginDate || !beginTime || !endDate || !endTime) {
                return false;
            }
            
            // 構建完整的日期時間字串
            const beginDateTime = new Date(beginDate + 'T' + beginTime);
            const endDateTime = new Date(endDate + 'T' + endTime);
            
            // 檢查日期是否有效
            if (isNaN(beginDateTime.getTime()) || isNaN(endDateTime.getTime())) {
                $('#gbtmsg').text('請輸入有效的日期時間').css('visibility', 'visible');
                return false;
            }
            
            const timeDiff = endDateTime.getTime() - beginDateTime.getTime();
            const hoursDiff = timeDiff / (1000 * 60 * 60);
            
            let isValid = true;
            let errorMessage = '';
            
            if (endDateTime <= beginDateTime) {
                isValid = false;
                errorMessage = '結束時間必須晚於開始時間';
            } else if (hoursDiff > 24) {
                isValid = false;
                errorMessage = '遊戲時間長度不能超過 24 小時';
            }
            
            if (!isValid) {
                $('#gbtmsg').text(errorMessage).css('visibility', 'visible');
                console.log('時間驗證失敗:', errorMessage);
            } else {
                $('#gbtmsg').css('visibility', 'hidden');
                console.log('時間驗證通過');
            }
            
            return isValid;
        }
        
        function updatePreview() {
            const title = $('#wizard_g_title').val() || '遊戲標題將顯示在這裡';
            const content = $('#wizard_g_content').val() || '遊戲描述將顯示在這裡';
            
            $('#preview-title').text(title);
            $('#preview-description').text(content.length > 100 ? content.substring(0, 100) + '...' : content);
        }
        
        function handleImageUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                
                if (file.size > 2 * 1024 * 1024) {
                    alert('圖片檔案大小不能超過2MB');
                    return;
                }
                
                if (!file.type.match('image.*')) {
                    alert('請選擇圖片檔案');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageUrl = e.target.result;
                    $('#wizard-game-icon').attr('src', imageUrl);
                    $('#preview-game-image').attr('src', imageUrl);
                    $('#game-icon').attr('src', imageUrl);
                };
                reader.readAsDataURL(file);
            }
        }
        
        function checkWizardF2Duplicate(f2Value) {
            var formElement = $('#form1');
            var userName = formElement.data('user-name');
            var gId = $("#g_id").val() || '';
            var f2ErrorElement = $('#wizard_g_f2_psw').siblings('.field-error');
            
            // 格式驗證
            if (!/^[a-zA-Z]{2,3}$/.test(f2Value)) {
                f2ErrorElement.text('請輸入2-3碼英文字母（區分大小寫）').show();
                return false;
            }
            
            // 保留字檢查
            if (userName !== 'Trial' && f2Value.toLowerCase().substr(0,1) === 't') {
                f2ErrorElement.text('此密碼代碼為保留字，請更換其他密碼').show();
                return false;
            }
            
            // 資料庫重複性檢查
            $.ajaxSetup({ async: false });
            var isDuplicate = false;
            
            $.get("API.php", {action: "SeachF2", f2: f2Value, g_id: gId}, function(data) {
                if (data !== '1') {
                    isDuplicate = true;
                }
            });
            
            if (isDuplicate) {
                f2ErrorElement.text('此密碼已被其他遊戲使用，請更換其他密碼').show();
                return false;
            }
            
            // 驗證通過
            f2ErrorElement.hide();
            return true;
        }
        
        function setupAutoSave() {
            if (!autoSaveEnabled) {
                setInterval(autoSave, 30000);
                autoSaveEnabled = true;
                console.log('自動保存功能已啟動');
            }
        }
        
        function autoSave() {
            if (!autoSaveEnabled) {
                console.log('自動保存未啟動，跳過');
                return; // 如果自動保存未啟動，則跳過
            }

            console.log('執行自動保存');
            const mapValue = $('#wizard_g_map_type').val();
            const userSelected = $('#wizard_g_map_type').data('user-selected');

            console.log('自動保存 - 地圖類型值:', mapValue, '用戶選擇標記:', userSelected);

            const formData = {
                g_title: $('#wizard_g_title').val(),
                g_content: $('#wizard_g_content').val(),
                g_f2_psw: $('#wizard_g_f2_psw').val(),
                g_team_count: $('#wizard_g_team_count').val(),
                g_begin_date: $('#wizard_g_begin_date').val(),
                g_begin_time: $('#wizard_g_begin_time').val(),
                g_end_date: $('#wizard_g_end_date').val(),
                g_end_time: $('#wizard_g_end_time').val(),
                g_map_type: mapValue,
                g_add: $('#wizard_g_add').val(),
                currentStep: currentWizardStep,
                step1Completed: autoSaveEnabled, // 只有在自動保存啟動後才標記為true
                userSelectedMap: userSelected, // 記錄用戶是否手動選擇了地圖
                timestamp: Date.now()
            };

            localStorage.setItem('gameManage_draft', JSON.stringify(formData));
        }
        
        function loadDraftData() {
            // 只在第一步驟時檢查草稿
            if (currentWizardStep !== 1) {
                console.log('不在第一步驟，跳過草稿檢查');
                return;
            }
            
            const draftData = localStorage.getItem('gameManage_draft');
            console.log('草稿數據:', draftData);
            
            if (draftData) {
                try {
                    const data = JSON.parse(draftData);
                    const timeDiff = Date.now() - data.timestamp;
                    
                    console.log('草稿檢查:', {
                        timeDiff: timeDiff,
                        step1Completed: data.step1Completed,
                        shouldPrompt: timeDiff < 24 * 60 * 60 * 1000 && data.step1Completed
                    });
                    
                    // 只有在24小時內且已完成第一步驟的草稿才提示恢復
                    if (timeDiff < 24 * 60 * 60 * 1000 && data.step1Completed) {
                        console.log('準備顯示草稿恢復提示');
                        setTimeout(() => {
                            if (confirm('檢測到未完成的遊戲創建草稿，是否要恢復？')) {
                                console.log('用戶選擇恢復草稿');
                                restoreDraftData(data);
                            } else {
                                console.log('用戶選擇不恢復草稿');
                            }
                        }, 500);
                    }
                } catch (e) {
                    console.error('Failed to parse draft data:', e);
                }
            }
        }
        
        function restoreDraftData(data) {
            $('#wizard_g_title').val(data.g_title || '');
            $('#wizard_g_content').val(data.g_content || '');
            $('#wizard_g_f2_psw').val(data.g_f2_psw || '');
            $('#wizard_g_team_count').val(data.g_team_count || '');
            $('#wizard_g_begin_date').val(data.g_begin_date || '');
            $('#wizard_g_begin_time').val(data.g_begin_time || '');
            $('#wizard_g_end_date').val(data.g_end_date || '');
            $('#wizard_g_end_time').val(data.g_end_time || '');

            // 地圖類型設置需要延遲，確保元素已加載
            setTimeout(() => {
                const userSelected = $('#wizard_g_map_type').data('user-selected');
                const mapType = data.g_map_type || '';

                // 如果草稿中有用戶選擇標記，恢復該標記
                if (data.userSelectedMap) {
                    $('#wizard_g_map_type').data('user-selected', true);
                    console.log('恢復用戶選擇標記');
                }

                // 恢復地圖類型值（無論是否為用戶選擇）
                if (mapType) {
                    console.log('恢復地圖類型:', mapType);
                    $('#wizard_g_map_type').val(mapType);
                    console.log('設置後地圖類型值:', $('#wizard_g_map_type').val());
                }
            }, 100);

            $('#wizard_g_add').val(data.g_add || '');

            updatePreview();

            if (data.currentStep && data.currentStep > 1) {
                goToStep(data.currentStep);
            }
        }
        
        
        function clearDraftData() {
            localStorage.removeItem('gameManage_draft');
            console.log('草稿數據已清除');
        }
        
        // 測試用：清除草稿的全局函數
        window.clearGameDraft = clearDraftData;
        
        function syncWizardToOriginalForm() {
            $('input[name="g_title"]').val($('#wizard_g_title').val());
            $('textarea[name="g_content"]').val($('#wizard_g_content').val());
            $('input[name="g_f2_psw"]').val($('#wizard_g_f2_psw').val());
            $('input[name="g_team_count"]').val($('#wizard_g_team_count').val());
            $('input[name="g_begin_date"]').val($('#wizard_g_begin_date').val());
            $('input[name="g_begin_time"]').val($('#wizard_g_begin_time').val());
            $('input[name="g_end_date"]').val($('#wizard_g_end_date').val());
            $('input[name="g_end_time"]').val($('#wizard_g_end_time').val());
            $('select[name="g_map_type"]').val($('#wizard_g_map_type').val());
            $('input[name="g_add"]').val($('#wizard_g_add').val());
            
            if ($('#wizard_uploadinput')[0].files.length > 0) {
                const file = $('#wizard_uploadinput')[0].files[0];
                const originalInput = $('input[name="uploadinput"]')[0];
                
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                originalInput.files = dataTransfer.files;
            }
        }
        
        // 向導提交函數
        function submitWizardData() {
            console.log('向導提交函數被調用');
            syncWizardToOriginalForm();

            if (validateAllSteps()) {
                clearDraftData();
                document.getElementById('form1').submit();
            }
        }

        // 重寫原始提交函數以支持向導模式
        function submitData() {
            console.log('提交函數被調用');

            // 檢查是否在向導模式中
            if ($('.game-creation-wizard').length > 0 && $('.game-creation-wizard').is(':visible')) {
                console.log('使用向導提交模式');
                submitWizardData();
                return;
            }

            // 原始提交邏輯
            console.log('使用原始提交模式');
            var formElement = $('#form1');
            var gameAddError8 = formElement.data('game-add-error-8');
            var gameAddError10 = formElement.data('game-add-error-10');
            var f2InputError = formElement.data('f2-input-error');

            if (document.getElementById('g_content').value == '') {
                alert(gameAddError8);
                return;
            }
            var f2Input = document.getElementById('g_f2_psw');
            if (f2Input.value == '') {
                $("#f2msg").css("display", "block").text(f2InputError);
                return;
            }
            // 先進行 F2 格式驗證
            if (!GetF2()) {
                return;
            }
            if (document.getElementById('g_team_count').value == '') {
                alert(gameAddError10);
                return;
            }
            if (!document.getElementById('g_rank_limit_checkbox').checked) {
                document.getElementById('g_rank_limit').value = '-1';
                document.getElementById('g_rank_limit').removeAttribute('disabled');
            }
            if (CheckBeginDate()) {
                ['g_f2_psw', 'g_team_count', 'g_begin_time', 'g_begin_date', 'g_end_time', 'g_end_date'].forEach(function(id) {
                    // Ensure element exists before trying to remove attribute
                    var element = document.getElementById(id);
                    if (element) {
                        element.removeAttribute('disabled');
                    }
                });
                document.getElementById('form1').submit();
            }
        }
        
        function validateAllSteps() {
            let isValid = true;
            
            for (let i = 1; i <= 2; i++) {
                const stepValid = validateStepData(i);
                if (!stepValid) {
                    isValid = false;
                    if (i < currentWizardStep) {
                        goToStep(i);
                    }
                    break;
                }
            }
            
            return isValid;
        }
        
        function validateStepData(step) {
            switch (step) {
                case 1:
                    return $('#wizard_g_title').val().trim() !== '' &&
                           $('#wizard_g_content').val().trim() !== '' &&
                           $('#wizard_g_f2_psw').val().trim() !== '' &&
                           $('#wizard_g_team_count').val().trim() !== '';
                case 2:
                    return $('#wizard_g_begin_date').val() !== '' &&
                           $('#wizard_g_begin_time').val() !== '' &&
                           $('#wizard_g_end_date').val() !== '' &&
                           $('#wizard_g_end_time').val() !== '' &&
                           $('#wizard_g_map_type').val() !== '' &&
                           validateTimeRange();
                default:
                    return true;
            }
        }
        
        // 日期格式轉換函數
        function formatDateForInput(dateStr) {
            if (!dateStr) return '';
            
            // 如果已經是 yyyy-MM-dd 格式，直接返回
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                return dateStr;
            }
            
            // 處理 MM/dd/yyyy 格式
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
                const parts = dateStr.split('/');
                return parts[2] + '-' + parts[0] + '-' + parts[1];
            }
            
            // 其他格式嘗試用 Date 對象轉換
            try {
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            } catch (e) {
                console.error('日期格式轉換失敗:', dateStr, e);
            }
            
            return '';
        }
        
        // 頁面加載完成後初始化向導
        $(document).ready(function() {
            console.log('頁面加載完成，初始化向導');
            console.log('jQuery 版本:', $.fn.jquery);
            console.log('地圖選單元素存在:', $('#wizard_g_map_type').length > 0);

            // 檢查日期格式是否正確
            const currentBeginDate = $('#wizard_g_begin_date').val();
            const currentEndDate = $('#wizard_g_end_date').val();

            console.log('當前日期值:', { currentBeginDate, currentEndDate });

            // 簡單的地圖選單初始化
            console.log('地圖選單初始化');
            const $mapSelect = $('#wizard_g_map_type');
            console.log('地圖選單元素:', $mapSelect.length > 0 ? '找到' : '未找到');
            console.log('選項數量:', $mapSelect.find('option').length);

            // 添加額外的調試信息
            console.log('修復版本已加載 - 版本 1.0');

            initializeWizard();
        });
        
    </script>
    {/literal}
    
    <style>
        /* 修正圖片上傳區域的圖片尺寸問題 */
        .game-photo img#game-icon {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
            display: block;
        }
        
        /* 確保圖片容器有固定尺寸 */
        .game-photo {
            overflow: hidden;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 時間設定區塊優化 */
        .date-time-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 5px;
        }
        
        /* 提交按鈕區塊樣式統一 */
        .submit-button-container {
            margin-top: 20px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .submit-button-container .btn {
            padding: 8px 20px;
            font-size: 14px;
            min-width: 100px;
        }
        
        /* =========================== */
        /* 遊戲創建向導樣式 (方案 C) */
        /* =========================== */
        
        .game-creation-wizard {
            display: flex;
            min-height: 600px;
            background: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 110%;
        }
        
        /* 側邊欄樣式 */
        .wizard-sidebar {
            width: 25%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
        }
        
        .progress-overview {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .progress-circle {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
            background: rgba(255,255,255,0.1);
        }
        
        .progress-circle::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #4CAF50;
            transform: rotate(var(--progress-angle, 0deg));
            transition: transform 0.3s ease;
        }
        
        .progress-percentage {
            font-size: 14px;
            font-weight: bold;
            color: white;
        }
        
        .progress-overview p {
            margin: 0;
            font-size: 12px;
            opacity: 0.9;
        }
        
        /* 步驟導航 */
        .step-navigation {
            flex: 1;
        }
        
        .nav-step {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            margin-bottom: 6px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }
        
        .nav-step:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .nav-step.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
        }
        
        .nav-step.completed {
            background: rgba(76,175,80,0.3);
        }
        
        .nav-step.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .step-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .step-details {
            flex: 1;
            min-width: 0;
        }
        
        .step-title {
            font-size: 13px;
            font-weight: 600;
            margin-right: 6px;
            display: inline;
            white-space: nowrap;
        }
        
        .step-time {
            font-size: 10px;
            opacity: 0.7;
            display: inline;
            white-space: nowrap;
        }
        
        /* 主要內容區域 */
        .wizard-content {
            width: 75%;
            padding: 40px 60px;
            background: white;
            position: relative;
            max-width: none;
        }
        
        .step-header {
            margin-bottom: 30px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
        }
        
        .step-header h2 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        
        .step-description {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        /* 步驟內容 */
        .step-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        
        .step-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 表單區塊 */
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .form-section h3 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .form-section h3 .section-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .form-row {
            margin-bottom: 20px;
        }
        
        .form-row:last-child {
            margin-bottom: 0;
        }
        
        /* 增強的表單控制元素 */
        .enhanced-input {
            position: relative;
            margin-bottom: 20px;
        }
        
        .enhanced-input label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .enhanced-input input,
        .enhanced-input textarea,
        .enhanced-input select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        /* 簡單的地圖選單樣式 */
        #wizard_g_map_type {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background-color: #fff;
            color: #333;
        }
        
        .enhanced-input input:focus,
        .enhanced-input textarea:focus,
        .enhanced-input select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
        }
        
        .enhanced-input.error input,
        .enhanced-input.error textarea,
        .enhanced-input.error select {
            border-color: #dc3545;
        }
        
        .enhanced-input.success input,
        .enhanced-input.success textarea,
        .enhanced-input.success select {
            border-color: #28a745;
        }
        
        .field-help {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .field-error {
            font-size: 12px;
            color: #dc3545;
            margin-top: 5px;
            display: none;
        }
        
        .enhanced-input.error .field-error {
            display: block;
        }
        
        .required-indicator {
            color: #dc3545;
            margin-left: 3px;
        }
        
        /* 預覽面板 */
        .preview-panel {
            position: sticky;
            top: 20px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            margin-top: 20px;
        }
        
        .preview-panel h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .preview-panel h4::before {
            content: '👁️';
            margin-right: 8px;
        }
        
        .game-preview {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .preview-image {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .preview-image img {
            max-width: 100px;
            max-height: 100px;
            border-radius: 4px;
            object-fit: cover;
        }
        
        .preview-info h5 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        
        .preview-info p {
            margin: 0;
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }
        
        /* 步驟控制按鈕 */
        .step-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .step-controls .btn {
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        
        
        .info-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .time-display {
            background: white;
            border-radius: 6px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }
        
        .time-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .time-item:last-child {
            border-bottom: none;
        }
        
        .time-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .time-value {
            color: #6c757d;
            font-size: 14px;
        }
        
        .duration-item {
            background: #e8f5e8;
            margin: 8px -15px -15px -15px;
            padding: 12px 15px 8px 15px;
            border-radius: 0 0 6px 6px;
        }
        
        .duration-item .time-label {
            color: #28a745;
        }
        
        .duration-item .time-value {
            color: #28a745;
            font-weight: 600;
        }
        
        .time-tips {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .time-tips li {
            padding: 6px 0;
            font-size: 13px;
            color: #6c757d;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .time-tips li::before {
            content: '•';
            color: #667eea;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        /* 響應式設計 */
        @media (max-width: 768px) {
            .game-creation-wizard {
                flex-direction: column;
            }
            
            .wizard-sidebar {
                width: 100%;
                padding: 20px;
            }
            
            .step-navigation {
                display: flex;
                overflow-x: auto;
                gap: 10px;
            }
            
            .nav-step {
                flex-shrink: 0;
                min-width: 200px;
            }
            
            .wizard-content {
                padding: 20px;
            }
            
            /* 移動端時間設定調整 */
            .form-section + .time-info-panel {
                display: none;
            }
        }
        
        /* 動畫效果 */
        .fade-in {
            animation: fadeIn 0.5s ease;
        }
        
        .slide-in {
            animation: slideIn 0.5s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        /* 隱藏原始表單 */
        .original-form {
            display: none;
        }
        
        .date-time-group input {
            flex: 0 0 auto;
        }
        
        /* 錯誤訊息統一樣式 */
        .error-message {
            color: #cc0000;
            font-family: 微軟正黑體, 'Microsoft JhengHei', sans-serif;
            font-size: 12px;
            font-weight: bold;
            line-height: 18px;
            margin-top: 5px;
        }
    </style>
</head>

<body>
<div class="wrapper">
    <div class="header-wrap">
        <div class="container">
            <div class="header">
                {include file='header.tpl'}
            </div>
        </div>
    </div>

    <div class="main-wrap-authenticated">
        <div class="container">
            <div class="main">
                <div class="title-section">
                    <div class="row">
                        <div class="span18">
                            <h2 class="ellipsis">{$lang.GameManage}</h2>
                        </div>
                    </div>
                </div>

                <div class="contents-wrap game-info">
                    <div class="row">
                        <div class="span4">
                            <div id="sidenav-fixed-container">
                                <div class="step-section">
                                    {include file='menu.tpl'}
                                </div>
                            </div>
                        </div>
                        <div id="sidenav-offset" class="span4"><br></div>

                        {* Add data attributes to the form tag for ALL needed dynamic values *}
                        <form id="form1" name="form1" action="?action=Save" method="POST" enctype="multipart/form-data"
                              data-f2-input-error="{$lang.F2InputError|escape:'html'}"
                              data-f2-reserved-error="{$lang.F2ReservedError|escape:'html'}"
                              data-f2-duplicate-error="{$lang.F2DuplicateError|escape:'html'}"
                              data-user-name="{$user_name|escape:'html'}"
                              data-game-add-error-8="{$lang.GameAddError_8|escape:'html'}"
                              data-game-add-error-10="{$lang.GameAddError_10|escape:'html'}"
                              data-credits-str-11="{$lang.CreditsStr_11|escape:'html'}"
                              data-game-add-error-5="{$lang.GameAddError_5|escape:'html'}"
                              data-time-format-error="{$lang.TimeFormatError|escape:'html'}"
                              data-user-state="{$user_state|escape:'html'}"
                              data-g-bounds="{$row.g_bounds|escape:'html'}"
                              data-end-date-before-start-date-error="結束日期不能早於開始日期"
                              data-duration-exceeds-24-hours-error="遊戲時間長度不能超過 24 小時">
                            <div class="span15">
                            
                            <!-- 遊戲創建向導界面 -->
                            <div class="game-creation-wizard">
                                <!-- 主內容區域 -->
                                <div class="wizard-content">
                                    <!-- 步驟 1：基本資訊 -->
                                    <div class="step-content active" data-step="1">
                                        <div class="step-header">
                                            <h2>基本資訊</h2>
                                            <p class="step-description">設定遊戲的基本資訊，包括標題、描述和主要圖片。這些資訊將在遊戲中顯示給參與者。</p>
                                        </div>
                                        
                                        <div class="form-section">
                                            <h3><span class="section-icon">📝</span>遊戲基本資料</h3>
                                            
                                            <div class="enhanced-input">
                                                <label for="wizard_g_title">遊戲標題 <span class="required-indicator">*</span></label>
                                                <input type="text" id="wizard_g_title" name="g_title" maxlength="40" 
                                                       value="{$row.g_title}" placeholder="請輸入遊戲標題">
                                                <div class="field-help">遊戲標題將顯示在手機端的遊戲列表中</div>
                                                <div class="field-error">請輸入遊戲標題</div>
                                            </div>
                                            
                                            <div class="enhanced-input">
                                                <label for="wizard_g_content">遊戲描述 <span class="required-indicator">*</span></label>
                                                <textarea id="wizard_g_content" name="g_content" rows="4" 
                                                          placeholder="請描述遊戲的規則、目標和玩法">{$row.g_content}</textarea>
                                                <div class="field-help">詳細描述遊戲的規則和目標，幫助參與者理解遊戲玩法</div>
                                                <div class="field-error">請輸入遊戲描述</div>
                                            </div>
                                            
                                            <div class="enhanced-input">
                                                <label for="wizard_g_f2_psw">F2 密碼 <span class="required-indicator">*</span></label>
                                                <input type="text" id="wizard_g_f2_psw" name="g_f2_psw" maxlength="3" 
                                                       value="{$row.g_f2_psw}" placeholder="請輸入 2-3 碼英文字母">
                                                <div class="field-help">參與者需要輸入此密碼來加入遊戲，支援手機帳號登入使用</div>
                                                <div class="field-error">請輸入有效的 F2 密碼</div>
                                            </div>
                                            
                                            <div class="enhanced-input">
                                                <label for="wizard_g_team_count">小隊數量 <span class="required-indicator">*</span></label>
                                                <input type="number" id="wizard_g_team_count" name="g_team_count" min="1" max="999" 
                                                       value="{if $row.g_team_count > 0}{$row.g_team_count}{elseif $user_name == 'Trial'}999{/if}" 
                                                       placeholder="請輸入小隊數量">
                                                <div class="field-help">設定遊戲中的小隊總數</div>
                                                <div class="field-error">請輸入有效的小隊數量</div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-section">
                                            <h3><span class="section-icon">🖼️</span>遊戲圖片</h3>
                                            
                                            <div class="enhanced-input">
                                                <label>遊戲主圖片 <span class="required-indicator">*</span></label>
                                                <div style="display: flex; align-items: flex-start; gap: 20px;">
                                                    <div>
                                                        <img id="wizard-game-icon" src="{if $row.g_photo != ''}{$row.g_photo}{else}images/game-pic.jpg{/if}" 
                                                             alt="遊戲圖片" style="width: 120px; height: 120px; object-fit: cover; border-radius: 8px; border: 2px solid #e9ecef;">
                                                    </div>
                                                    <div>
                                                        <input type="file" id="wizard_uploadinput" name="uploadinput" accept="image/*" style="display: none;">
                                                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('wizard_uploadinput').click();">選擇圖片</button>
                                                        <div class="field-help" style="margin-top: 10px;">
                                                            建議尺寸：正方形 (300x300)<br>
                                                            支援格式：PNG, JPG, BMP<br>
                                                            檔案大小：< 2MB
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="field-error">請上傳遊戲圖片</div>
                                            </div>
                                        </div>
                                        
                                        <!-- 即時預覽 -->
                                        <div class="preview-panel">
                                            <h4>遊戲預覽</h4>
                                            <div class="game-preview">
                                                <div class="preview-image">
                                                    <img id="preview-game-image" src="{if $row.g_photo != ''}{$row.g_photo}{else}images/game-pic.jpg{/if}" alt="遊戲圖片預覽">
                                                </div>
                                                <div class="preview-info">
                                                    <h5 id="preview-title">{if $row.g_title}{$row.g_title}{else}遊戲標題將顯示在這裡{/if}</h5>
                                                    <p id="preview-description">{if $row.g_content}{$row.g_content|truncate:100}{else}遊戲描述將顯示在這裡{/if}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 步驟 2：時間設定 -->
                                    <div class="step-content" data-step="2">
                                        <div class="step-header">
                                            <h2>時間設定</h2>
                                            <p class="step-description">設定遊戲的開始和結束時間，以及選擇地圖類型。</p>
                                        </div>
                                        
                                        <div class="form-section">
                                            <h3><span class="section-icon">⏰</span>遊戲時間設定</h3>
                                                
                                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                                    <div class="enhanced-input">
                                                        <label for="wizard_g_begin_date">開始日期 <span class="required-indicator">*</span></label>
                                                        <input type="date" id="wizard_g_begin_date" name="g_begin_date" 
                                                               value="{if $row.g_begin_date}{$row.g_begin_date|regex_replace:'/^(\d{2})\/(\d{2})\/(\d{4})$/':'$3-$1-$2'}{/if}">
                                                        <div class="field-error">請選擇開始日期</div>
                                                    </div>
                                                    
                                                    <div class="enhanced-input">
                                                        <label for="wizard_g_begin_time">開始時間 <span class="required-indicator">*</span></label>
                                                        <input type="time" id="wizard_g_begin_time" name="g_begin_time" 
                                                               value="{$row.g_begin_time}">
                                                        <div class="field-error">請選擇開始時間</div>
                                                    </div>
                                                    
                                                    <div class="enhanced-input">
                                                        <label for="wizard_g_end_date">結束日期 <span class="required-indicator">*</span></label>
                                                        <input type="date" id="wizard_g_end_date" name="g_end_date" 
                                                               value="{if $row.g_end_date}{$row.g_end_date|regex_replace:'/^(\d{2})\/(\d{2})\/(\d{4})$/':'$3-$1-$2'}{/if}">
                                                        <div class="field-error">請選擇結束日期</div>
                                                    </div>
                                                    
                                                    <div class="enhanced-input">
                                                        <label for="wizard_g_end_time">結束時間 <span class="required-indicator">*</span></label>
                                                        <input type="time" id="wizard_g_end_time" name="g_end_time" 
                                                               value="{$row.g_end_time}">
                                                        <div class="field-error">請選擇結束時間</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="field-help">遊戲時間長度不能超過 24 小時</div>
                                                <div id="gbtmsg" class="field-error" style="visibility:hidden; margin-top: 10px;"></div>
                                            </div>
                                        
                                        <div class="form-section">
                                            <h3><span class="section-icon">🗺️</span>地圖設定</h3>
                                            
                                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                                <div>
                                                    <label for="wizard_g_map_type">地圖類型 <span style="color: red;">*</span></label><br>
                                                    <select id="wizard_g_map_type" name="g_map_type">
                                                        <option value="" {if $row.g_map_type == '' || $row.g_map_type == '0'}selected{/if}>請選擇地圖類型</option>
                                                        <option value="1" {if $row.g_map_type == '1'}selected{/if}>Google地圖 (台灣)</option>
                                                        <option value="2" {if $row.g_map_type == '2'}selected{/if}>OSM(臺灣)</option>
                                                        <option value="3" {if $row.g_map_type == '3'}selected{/if}>高德地圖（臺灣）</option>
                                                        <option value="4" {if $row.g_map_type == '4'}selected{/if}>高德地圖 (中國)</option>
                                                        <option value="5" {if $row.g_map_type == '5'}selected{/if}>OSM 地圖(中國)</option>
                                                    </select><br>
                                                    <small>選擇遊戲中使用的地圖服務類型</small>
                                                </div>
                                                
                                                <div class="enhanced-input">
                                                    <label for="wizard_g_add">遊戲附加資訊 <span style="color: #6c757d;">(可選)</span></label>
                                                    <input type="text" id="wizard_g_add" name="g_add" maxlength="200" 
                                                           value="{$row.g_add}" placeholder="輸入額外的遊戲資訊">
                                                    <div class="field-help">可以輸入額外的遊戲相關資訊</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 步驟控制按鈕 -->
                                    <div class="step-controls">
                                        <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" disabled>
                                            ← 上一步
                                        </button>
                                        <div>
                                            <span class="step-indicator">步驟 <span id="currentStep">1</span> / 6</span>
                                        </div>
                                        <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                                            下一步 →
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 側邊欄 -->
                                <div class="wizard-sidebar">
                                    <div class="progress-overview">
                                        <div class="progress-circle">
                                            <span class="progress-percentage">0%</span>
                                        </div>
                                        <p>預計需要 12-15 分鐘</p>
                                    </div>
                                    
                                    <div class="step-navigation">
                                        <div class="nav-step active" data-step="1">
                                            <div class="step-icon">📝</div>
                                            <div class="step-details">
                                                <span class="step-title">基本資訊</span>
                                                <span class="step-time">(3-5分)</span>
                                            </div>
                                        </div>
                                        <div class="nav-step disabled" data-step="2">
                                            <div class="step-icon">⏰</div>
                                            <div class="step-details">
                                                <span class="step-title">時間設定</span>
                                                <span class="step-time">(2-3分)</span>
                                            </div>
                                        </div>
                                        <div class="nav-step disabled" data-step="3">
                                            <div class="step-icon">🎯</div>
                                            <div class="step-details">
                                                <span class="step-title">遊戲規則</span>
                                                <span class="step-time">(3-4分)</span>
                                            </div>
                                        </div>
                                        <div class="nav-step disabled" data-step="4">
                                            <div class="step-icon">🔧</div>
                                            <div class="step-details">
                                                <span class="step-title">進階功能</span>
                                                <span class="step-time">(2-3分)</span>
                                            </div>
                                        </div>
                                        <div class="nav-step disabled" data-step="5">
                                            <div class="step-icon">🎨</div>
                                            <div class="step-details">
                                                <span class="step-title">視覺設定</span>
                                                <span class="step-time">(2-3分)</span>
                                            </div>
                                        </div>
                                        <div class="nav-step disabled" data-step="6">
                                            <div class="step-icon">✅</div>
                                            <div class="step-details">
                                                <span class="step-title">最終確認</span>
                                                <span class="step-time">(1-2分)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 原始表單（隱藏） -->
                            <div class="original-form">
                                <div class="picture-section">
                                    <div class="row">
                                        <div class="span4 game-photo">
                                            <img id="game-icon" src="{if $row.g_photo != ''}{$row.g_photo}{else}images/game-pic.jpg{/if}" alt="">
                                            <input type="hidden" name="u_id" value="{$row.u_id}">
                                            <input type="hidden" id="g_id" name="g_id" value="{$row.g_id}">
                                        </div>
                                        <div class="span4_1 upload-section">
                                            <a class="link btn" href="?action=Delete" onClick="if(!confirm('{$lang.GameDeleteInfo}'))return false;">{$lang.GameDelete}</a>
                                            <span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameDeleteQA}</span></span><br><br>
                                            <input name="uploadinput" onChange="readUrl(this)" type="file" id="uploadinput" accept="image/*" style="display:none;">
                                            <input type="hidden" name="g_photo" value="{$row.g_photo}">
                                            <a class="link btn" href="javascript:void(0)" onClick="this.style.display='none';document.getElementById('uploadinput').style.display='block';">{$lang.UpLoadPhoto}</a>
                                            <span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.UpLoadPhotoQA}</span></span>{$lang.Required}<br>
                                            <span id="upload-detail"></span>
                                            <p id="photo-info" class="mt-5">{$lang.GameStr1}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="game-form_1">
                                    <div class="game-form-input-spacing">
                                        <label>{$lang.GameTitle}{$lang.Required}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameTitleQA}</span></span></label>
                                        <input name="g_title" type="text" class="text-field span5" id="g_title" value="{$row.g_title}" maxlength="40" {if $gameLevelType.set_game_name != 1}readonly="readonly"{/if}>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label>{$lang.GameContent}{$lang.Required}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameContentQA}</span></span></label>
                                        <textarea id="g_content" rows="10" cols="40" name="g_content" class="textarea2 height-100 span5" {$over} {if $gameLevelType.set_game_desc != 1}readonly="readonly"{/if}>{$row.g_content}</textarea>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label>{if isset($lang.GameScoreType)}{$lang.GameScoreType}{else}遊戲計分方式{/if}{$lang.Required}</label>
                                        <input type="radio" name="g_type" value="1" checked {$overGameBeginTime} />points (計分制)<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.PointTypeQA}</span></span><br/>
                                        <span style="margin-left: 20px;">a </span><input name="g_point_a" type="text" class="text-field span5" style="width: 80px;" value="{$row.g_point_a}" maxlength="40" {if $user_state != "1"}{$overGameBeginTime}{/if}><br/>
                                        <span style="margin-left: 20px;">b </span><input name="g_point_b" type="text" class="text-field span5" style="width: 80px;" value="{$row.g_point_b}" maxlength="40" {if $user_state != "1"}{$overGameBeginTime}{/if}><br/>
                                        <span style="margin-left: 20px;">c </span><input name="g_point_c" type="text" class="text-field span5" style="width: 80px;" value="{$row.g_point_c}" maxlength="40" {if $user_state != "1"}{$overGameBeginTime}{/if}><br/>
                                        <span style="margin-left: 20px;">d </span><input name="g_point_d" type="text" class="text-field span5" style="width: 80px;" value="{$row.g_point_d}" maxlength="40" {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameMapTypeQA}</span></span>{$lang.GameMapType}{$lang.Required}</label>
                                        <select class="select customSelect select-250" name="g_map_type" style="width: 140px;" {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                            <option value="1" {if $row.g_map_type == '1'}selected{/if}>{$lang.MapType1}</option>
                                            <option value="2" {if $row.g_map_type == '2'}selected{/if}>{$lang.MapType2}</option>
                                            <option value="3" {if $row.g_map_type == '3'}selected{/if}>{$lang.MapType3}</option>
                                            <option value="4" {if $row.g_map_type == '4'}selected{/if}>{$lang.MapType4}</option>
                                            <option value="5" {if $row.g_map_type == '5'}selected{/if}>{$lang.MapType5}</option>
                                        </select>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameAddQA}</span></span>{$lang.GameAdd}{$lang.Optional}</label>
                                        <input name="g_add" type="text" class="text-field span5" id="g_add" style="width: 120px;" value="{$row.g_add}" maxlength="200" {$over}>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.F2PSWQA}</span></span>{$lang.F2PSW} {$lang.Required}</label>
                                        <input name="g_f2_psw" type="text" class="text-field span5" id="g_f2_psw" onChange="GetF2();" style="width: 70px; margin-left: 8px;" value="{$row.g_f2_psw}" maxlength="3" {$f2disabled} {$over}>
                                        <div style="color:#cc0000; font-family:微軟正黑體; font-size:12px; font-weight:bold; line-height:18px; margin-top:5px;" id="f2msg"></div>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameTeamCountQA}</span></span>{$lang.GameTeamCount}{$lang.Required}</label>
                                        <input name="g_team_count" type="text" class="text-field span5" id="g_team_count" style="width: 70px;" value="{if $row.g_team_count > 0}{$row.g_team_count}{elseif $user_name == 'Trial'}999{/if}" maxlength="30" {$disabled} {$over}>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameTeamCheckQA}</span></span>{$lang.GameTeamCheck}</label>
                                        <input name="g_team_check" type="text" class="text-field span5" id="g_team_check" style="width: 70px;" value="{$row.g_team_check}" maxlength="30" {$over} onkeypress="return isValidGroups(event)" {if $gameLevelType.set_feed_multi != 1}readonly="readonly"{/if}>
                                        <span id="checkMsg" style="color:#cc0000"></span>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.BenginDateQA}</span></span>{$lang.BenginDate}{$lang.Required}</label>
                                        <div class="date-time-group">
                                            <input name="g_begin_date" type="text" class="text-field span5" id="g_begin_date" style="width:110px;" value="{$row.g_begin_date}" maxlength="40" {* Removed onChange="changeBeginDate()" *} {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                            <input name="g_begin_time" value="{$row.g_begin_time}" class="text-field span5" maxlength="40" type="text" id="g_begin_time" style="width:110px;" {* Removed onChange="changeBeginDate()" *} {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                        </div>
                                    </div>

                                    <div class="game-form-input-spacing">
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.EndDateQA}</span></span>{$lang.EndDate}{$lang.Required}</label>
                                        <div class="date-time-group">
                                            <input name="g_end_date" type="text" class="text-field span5" id="g_end_date" style="width:110px;" value="{$row.g_end_date}" maxlength="40" {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                            <input name="g_end_time" value="{$row.g_end_time}" class="text-field span5" maxlength="40" type="text" id="g_end_time" style="width:110px;" {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                        </div>
                                        <div id="gbtmsg" class="error-message" style="visibility:hidden;"></div>
                                    </div>

                                    <div {if $gameLevelType.set_app_feed_control != 1}style="display:none"{/if}>
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.FeedQA}</span></span>{$lang.Feed}</label>
                                        <input id="g_feed" name="g_feed" type="checkbox" value="1" {if $row.g_feed == 1}checked="checked"{/if}>
                                    </div>

                                    <div class="game-form-input-spacing game-form-input-comboxes">
                                        <ul>
                                            <li {if $gameLevelType.set_game_sorting_points_type != 1}style="display:none"{/if}>
                                                <input id="g_sorting" name="g_sorting" type="checkbox" value="1" {if $row.g_sorting == '1'}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}> {$lang.SortingMode}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.SortingModeQA}</span></span>
                                                (<input id="g_sorting_show_all" name="g_sorting_show_all" type="checkbox" value="1" {if $row.g_sorting_show_all == '1'}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}> {$lang.SortingModeShowAll}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.SortingModeShowAllQA}</span></span>)
                                            </li>
                                            <li {if $gameLevelType.set_game_show_group != 1}style="display:none"{/if}>
                                                <input id="g_crowd" name="g_crowd" type="checkbox" value="1" {if $row.g_crowd == '1'}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}> {$lang.CrowdMode}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.CrowdModeQA}</span></span>
                                            </li><br>
                                            <li {if $gameLevelType.set_mission_special_location_trigger != 1}style="display:none"{/if}>
                                                <input id="g_trigger" name="g_trigger" type="checkbox" value="1" {if $row.g_trigger == '1'}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}> {$lang.MissionsStr_27}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.MissionsStr_27QA}</span></span>
                                            </li>
                                            <li {if $gameLevelType.set_app_share_photo_all_team != 1}style="display:none"{/if}>
                                                <input id="g_share_photo_all_team" name="g_share_photo_all_team" type="checkbox" value="1" {if $row.g_share_photo_all_team == '1'}checked{/if}> {$lang.MyGame_4}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.MyGame_4QA}</span></span>
                                            </li>
                                            <li {if $gameLevelType.set_auto_feed_pass_func != 1}style="display:none"{/if}>
                                                <input id="g_auto_feed_pass" name="g_auto_feed_pass" type="checkbox" value="1" {if $row.g_auto_feed_pass != 0}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}>{$lang.GameAutoPass}<span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameAutoPassQA}</span></span>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="game-form-input-spacing" {if $gameLevelType.set_enable_rank_limit != 1}style="display:none"{/if}>
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameRankLimitQA}</span></span>{$lang.GameRankLimit}</label>
                                        <input id="g_rank_limit_checkbox" name="g_rank_limit_checkbox" type="checkbox" value="1" {if $row.g_rank_limit != -1}checked{/if} onChange="changeDisable()">
                                        <input name="g_rank_limit" onkeypress="return isNumberKey(event)" class="text-field span5" id="g_rank_limit" style="width: 70px;" value="{$row.g_rank_limit}" {if $row.g_rank_limit == -1}disabled{/if} maxlength="10">
                                    </div>

                                    <div class="game-form-input-spacing" {if $gameLevelType.set_game_setted_f2_login != 1}style="display:none"{/if}>
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.LockF2QA}</span></span>{$lang.LockF2}</label>
                                        <input id="g_lock_f2" name="g_lock_f2" type="checkbox" value="1" {if $row.g_lock_f2 == 1}checked{/if} {if $user_state != "1"}{$overGameBeginTime}{/if}>
                                    </div>

                                    <div class="game-form-input-spacing" {if $gameLevelType.set_lonlat_to_twd97 != 1}style="display:none"{/if}>
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.LonLatToTWD97QA}</span></span>{$lang.LonLatToTWD97}</label>
                                        <input id="g_lonlat_to_twd97" name="g_lonlat_to_twd97" type="checkbox" value="1" {if $row.g_lonlat_to_twd97 == 1}checked{/if}>
                                    </div>

                                    <div class="span4 game-photo" {if $gameLevelType.set_upload_user_logo != 1}style="display:none"{/if}>
                                        <img id="game-logo" src="{if $row.g_user_logo == ''}images/game-pic.jpg{else}{$row.g_user_logo}{/if}" alt="" style="width: 225px; height: 50px;">
                                        <input type="hidden" name="g_user_logo" id="g_user_logo" value="{$row.g_user_logo}">
                                    </div>

                                    <div class="span7 upload-section" style="float:left; width:220px; {if $gameLevelType.set_upload_user_logo != 1}display:none{/if}">
                                        <input name="uploadinputlogo" type="file" id="uploadinputlogo" onChange="readUrl(this)" accept="image/*" style="display:none; margin-top:13px;">
                                        <a class="link btn" href="javascript:void(0)" style="margin-left: 20px; margin-top:13px;" onClick="this.style.display='none';document.getElementById('uploadinputlogo').style.display='block';">{$lang.UpLoadPhoto}{$lang.Optional}</a>
                                        <span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.AdminUserPhotoContentQA}</span></span><br>
                                        <span id="upload-detail-logo"></span>
                                        <div style="float:left;">
                                            <p id="photo-info" class="mt-5" style="margin-left:15px; margin-top: 30px;">{$lang.PhotoSize}225*50<br>{$lang.AdminUserPhotoContent}</p>
                                        </div>
                                    </div>

                                    {* ========== 背景主題設定區塊 ========== *}
                                    <div class="game-form-input-spacing" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 15px; margin: 20px 0; background-color: #f9f9f9;">
                                        <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px;">
                                            <span class="tooltip">
                                                <img src="../question_mark_2.png" />
                                                <span class="tooltiptext">為此遊戲設定背景主題和背景圖片，影響手機端的視覺體驗</span>
                                            </span>
                                            背景主題設定
                                        </h3>
                                        
                                        {* 主題類型選擇 *}
                                        <div class="game-form-input-spacing">
                                            <label>
                                                <span class="tooltip">
                                                    <img src="../question_mark_2.png" />
                                                    <span class="tooltiptext">選擇遊戲的視覺主題風格，影響手機端的背景顏色和視覺效果</span>
                                                </span>
                                                主題類型 <span style="color: #4CAF50; font-weight: bold;">必選</span>
                                            </label>
                                            <select name="theme_type" id="theme_type" class="select customSelect select-250" style="width: 200px;" onchange="updateThemePreview()">
                                                <option value="original" {if $row.theme_type == 'original' || !$row.theme_type}selected{/if}>原始純主題</option>
                                                <option value="default" {if $row.theme_type == 'default'}selected{/if}>經典藍</option>
                                                <option value="marvel" {if $row.theme_type == 'marvel'}selected{/if}>漫威紅</option>
                                                <option value="harry_potter" {if $row.theme_type == 'harry_potter'}selected{/if}>魔法金</option>
                                                <option value="adventure" {if $row.theme_type == 'adventure'}selected{/if}>探險綠</option>
                                                <option value="mystery" {if $row.theme_type == 'mystery'}selected{/if}>神秘紫</option>
                                                <option value="ocean" {if $row.theme_type == 'ocean'}selected{/if}>海洋藍</option>
                                            </select>
                                            <div id="theme-preview" style="width: 200px; height: 60px; border: 2px solid #ddd; border-radius: 4px; margin-top: 10px; background: linear-gradient(135deg, #FAFAFA, #FAFAFA);"></div>
                                        </div>
                                        
                                        {* 背景狀態開關 *}
                                        <div class="game-form-input-spacing">
                                            <label>
                                                <span class="tooltip">
                                                    <img src="../question_mark_2.png" />
                                                    <span class="tooltiptext">控制背景主題是否在手機端生效。停用時將使用原始app樣式</span>
                                                </span>
                                                背景狀態
                                            </label>
                                            <div style="margin-top: 8px;">
                                                <input type="radio" id="bg_status_active" name="background_status" value="active" {if $row.background_status == 'active'}checked{/if} onchange="toggleBackgroundOptions()">
                                                <label for="bg_status_active" style="margin-left: 5px; margin-right: 20px; color: #4CAF50; font-weight: bold;">啟用</label>
                                                
                                                <input type="radio" id="bg_status_inactive" name="background_status" value="inactive" {if $row.background_status != 'active'}checked{/if} onchange="toggleBackgroundOptions()">
                                                <label for="bg_status_inactive" style="margin-left: 5px; color: #999;">停用</label>
                                            </div>
                                        </div>
                                        
                                        {* 背景圖片上傳區塊 *}
                                        <div id="background-image-section" style="opacity: {if $row.background_status == 'active'}1{else}0.5{/if};">
                                            <div class="game-form-input-spacing">
                                                <label>
                                                    <span class="tooltip">
                                                        <img src="../question_mark_2.png" />
                                                        <span class="tooltiptext">上傳自定義背景圖片（可選）。建議尺寸：1920x1080，檔案大小 < 2MB</span>
                                                    </span>
                                                    背景圖片 <span style="color: #999;">可選</span>
                                                </label>
                                                <div style="display: flex; align-items: flex-start; gap: 15px;">
                                                    <div>
                                                        <input type="file" name="background_image_upload" id="background_image_upload" accept="image/jpeg,image/png,image/webp" style="display: none;" onchange="previewBackgroundImage(this)">
                                                        <input type="hidden" name="background_image" value="{$row.background_image}">
                                                        <input type="hidden" name="background_thumb" value="{$row.background_thumb}">
                                                        
                                                        <a class="link btn" href="javascript:void(0)" onclick="document.getElementById('background_image_upload').click();">
                                                            選擇背景圖片
                                                        </a>
                                                        <div id="bg-upload-info" style="margin-top: 5px; font-size: 12px; color: #666;">
                                                            支援 JPG, PNG, WebP<br>
                                                            建議尺寸: 1920x1080<br>
                                                            檔案大小 < 2MB
                                                        </div>
                                                    </div>
                                                    
                                                    <div>
                                                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">預覽圖:</div>
                                                        <img id="bg-preview" src="{if $row.background_thumb}{$row.background_thumb}{else}images/no-image.png{/if}" 
                                                             style="width: 120px; height: 80px; border: 2px solid #ddd; border-radius: 4px; object-fit: cover;" 
                                                             alt="背景預覽">
                                                        <div id="bg-file-info" style="margin-top: 5px; font-size: 11px; color: #888;">
                                                            {if $row.background_image}已上傳{else}未選擇檔案{/if}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="game-form-input-spacing" {if $gameLevelType.set_app_share_photo_owa != 1}style="display:none"{/if}>
                                        <label><span class="tooltip"><img src="../question_mark_2.png" /><span class="tooltiptext">{$lang.GameOption_AppSharePhotoQA}</span></span>{$lang.GameOption_AppSharePhoto}</label>
                                        <input type="checkbox" id="g_app_sharephoto" name="g_app_sharephoto" value="1" {if $row.g_app_sharephoto == 1}checked{/if}>
                                    </div>

                                    <div class="game-form-input-spacing" {if $gameLevelType.set_view_video != 1}style="display:none"{/if}>
                                        <div class="mission-question-hd">{$lang.AppViewVideoOption}</div>
                                        <div class="mission-question-row">
                                            <input type="radio" id="set_view_video_radio_1" name="g_view_video" value="0" {if $row.g_view_video == '0'}checked{/if}>
                                            <span>{$lang.AppViewVideoOption_NotAllowed}</span>
                                        </div>
                                        <div class="mission-question-row">
                                            <input type="radio" id="set_view_video_radio_2" name="g_view_video" value="1" {if $row.g_view_video == '1'}checked{/if}>
                                            <span>{$lang.AppViewVideoOption_GroupLeaderOnly}</span>
                                        </div>
                                        <div class="mission-question-row">
                                            <input type="radio" id="set_view_video_radio_3" name="g_view_video" value="2" {if $row.g_view_video == '2'}checked{/if}>
                                            <span>{$lang.AppViewVideoOption_AllMembers}</span>
                                        </div>
                                    </div>

                                    <div class="game-form-input-spacing {if $row.g_trial != 1}hide{/if}">
                                        <label>{$lang.Gps}</label>
                                        <input type="checkbox" id="g_gps" name="g_gps" onclick="onGps()">
                                    </div>

                                    <div id="map-canvas" {if $row.g_trial != 1}class="hide"{/if}></div>
                                    <input id="g_bounds" name="g_bounds" class="hide">

                                    <div class="submit-button-container">
                                        <input type="button" value="{$lang.Definite}" class="submit btn btn-success add-btn" onClick="submitData();" />
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer-wrap-authenticated">
        <div class="container">
            <div class="footer row"></div>
        </div>
    </div>
</div>
</body>
</html>
