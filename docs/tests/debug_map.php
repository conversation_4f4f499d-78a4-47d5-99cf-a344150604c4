<?php
// 最基礎的診斷檔案
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>診斷報告</h1>";
echo "<p>PHP版本: " . phpversion() . "</p>";
echo "<p>當前時間: " . date('Y-m-d H:i:s') . "</p>";

// 檢查參數
$g_id = isset($_REQUEST['g_id']) ? intval($_REQUEST['g_id']) : 0;
echo "<p>接收到的g_id: {$g_id}</p>";

// 逐步測試include檔案
echo "<h2>測試include檔案</h2>";

try {
    echo "<p>1. 測試config.php...</p>";
    require_once 'inc/config.php';
    echo "<p>✅ config.php 載入成功</p>";
} catch (Exception $e) {
    echo "<p>❌ config.php 錯誤: " . $e->getMessage() . "</p>";
    exit;
}

try {
    echo "<p>2. 測試sql_class.php...</p>";
    require_once 'inc/sql_class.php';
    echo "<p>✅ sql_class.php 載入成功</p>";
} catch (Exception $e) {
    echo "<p>❌ sql_class.php 錯誤: " . $e->getMessage() . "</p>";
    exit;
}

try {
    echo "<p>3. 測試function.php...</p>";
    require_once 'inc/function.php';
    echo "<p>✅ function.php 載入成功</p>";
} catch (Exception $e) {
    echo "<p>❌ function.php 錯誤: " . $e->getMessage() . "</p>";
    exit;
}

// 測試資料庫連線
echo "<h2>測試資料庫連線</h2>";
if (isset($db)) {
    echo "<p>✅ \$db 變數存在</p>";
    echo "<p>資料庫對象類型: " . get_class($db) . "</p>";
    
    try {
        $test_query = $db->query("SELECT 1 as test");
        if ($test_query) {
            echo "<p>✅ 資料庫查詢成功</p>";
        } else {
            echo "<p>❌ 資料庫查詢失敗</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 資料庫查詢異常: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ \$db 變數不存在</p>";
}

// 測試遊戲查詢
if ($g_id > 0 && isset($db)) {
    echo "<h2>測試遊戲查詢</h2>";
    try {
        $sql = "SELECT g_id, g_title, g_begin_date, g_end_date FROM game WHERE g_id = {$g_id}";
        echo "<p>SQL: {$sql}</p>";
        
        $result = $db->query($sql);
        if ($result) {
            $row = $db->fetch_array($result);
            if ($row) {
                echo "<p>✅ 找到遊戲: " . htmlspecialchars($row['g_title']) . "</p>";
            } else {
                echo "<p>❌ 遊戲ID {$g_id} 不存在</p>";
            }
        } else {
            echo "<p>❌ 查詢執行失敗</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 查詢異常: " . $e->getMessage() . "</p>";
    }
}

// 檢查必要函數
echo "<h2>檢查必要函數</h2>";
if (function_exists('GetGameUserList')) {
    echo "<p>✅ GetGameUserList 函數存在</p>";
} else {
    echo "<p>❌ GetGameUserList 函數不存在</p>";
}

echo "<h2>完成診斷</h2>";
echo "<p>如果以上都正常，問題可能在RealTimeTeamMap.php的具體實作中</p>";
?>