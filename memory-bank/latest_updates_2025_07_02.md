# 最新更新記錄 - 2025年7月2日

本文件記錄了 PilotRun 平台最近的更新和修改。

## 🆕 即時小隊位置系統 - 2025年7月2日

### 新增功能
1. **RealTimeMap.php** - 全新的即時小隊位置顯示系統
   - 支援 4 種地圖類型（Google、高德、百度、OSM）
   - 即時更新團隊位置（5分鐘自動刷新）
   - 時間軸回顧功能，可查看歷史位置
   - 聯盟顏色編碼系統

2. **Chart.tpl 更新** - 在得分曲線圖頁面添加「小隊動態」連結
   - 位置：「回顧路徑」連結右側
   - 支援所有地圖類型

### 技術實現
- 獨立運行設計，不依賴外部 PHP 檔案
- 客戶端時間軸數據生成
- 響應式設計，支援手機和桌面瀏覽

### 訪問方式
- 直接訪問：`https://test.pilotrunapp.com/RealTimeMap.php?g_id={遊戲ID}`
- 從 Chart.php 頁面點擊「小隊動態」連結

### 相關文件
- 開發記錄：`memory-bank/realtime_team_map_development_2025_07_02.md`
- 技術參考：`memory-bank/realtime_map_technical_reference.md`
- 測試檔案備份：`to_be_remove/debug_2025_07_02/`

---

## 🐛 聯盟任務積分修復 - 2025年7月1日

### 修復內容
1. **ScoreAPI.php** - 修復聯盟任務積分累積錯誤
   - 問題：將所有聯盟分數累加到單一聯盟
   - 解決：正確初始化各聯盟積分為 0

2. **Chart.php** - 修復 `$game_info['g_name']` 未定義錯誤
   - 修正為使用 `g_title` 欄位

### 相關文件
- 開發記錄：`memory-bank/alliance_scoring_bug_fix_2025_07_01.md`

---

## 📚 CLAUDE.md 文件系統 - 2025年6月

### 新增功能
- 建立專案知識庫文件系統
- 記錄開發慣例和技術規範
- 整理 API 端點和資料庫結構

### 主要文件
- `CLAUDE.md` - 主要專案知識庫
- `memory-bank/` - 開發記錄資料夾