<?php
/*
 * This file is part of Smarty.
 *
 * (c) 2015 Uwe Tews
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Smarty_Internal_Templatelexer
 * This is the template file lexer.
 * It is generated from the smarty_internal_templatelexer.plex file
 *
 *
 * <AUTHOR> Tews <<EMAIL>>
 */
class Smarty_Internal_Templatelexer
{
    /**
     * Source
     *
     * @var string
     */
    public $data;

    /**
     * Source length
     *
     * @var int
     */
    public $dataLength = null;

    /**
     * byte counter
     *
     * @var int
     */
    public $counter;

    /**
     * token number
     *
     * @var int
     */
    public $token;

    /**
     * token value
     *
     * @var string
     */
    public $value;

    /**
     * current line
     *
     * @var int
     */
    public $line;

    /**
     * tag start line
     *
     * @var
     */
    public $taglineno;

    /**
     * php code type
     *
     * @var string
     */
    public $phpType = '';

   /**
     * state number
     *
     * @var int
     */
    public $state = 1;

    /**
     * Smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * trace file
     *
     * @var resource
     */
    public $yyTraceFILE;

    /**
     * trace prompt
     *
     * @var string
     */
    public $yyTracePrompt;

    /**
     * XML flag true while processing xml
     *
     * @var bool
     */
    public $is_xml = false;

    /**
     * state names
     *
     * @var array
     */
    public $state_name = array(1 => 'TEXT', 2 => 'TAG', 3 => 'TAGBODY', 4 => 'LITERAL', 5 => 'DOUBLEQUOTEDSTRING',);

    /**
     * token names
     *
     * @var array
     */
    public $smarty_token_names = array(        // Text for parser error messages
                                               'NOT'         => '(!,not)',
                                               'OPENP'       => '(',
                                               'CLOSEP'      => ')',
                                               'OPENB'       => '[',
                                               'CLOSEB'      => ']',
                                               'PTR'         => '->',
                                               'APTR'        => '=>',
                                               'EQUAL'       => '=',
                                               'NUMBER'      => 'number',
                                               'UNIMATH'     => '+" , "-',
                                               'MATH'        => '*" , "/" , "%',
                                               'INCDEC'      => '++" , "--',
                                               'SPACE'       => ' ',
                                               'DOLLAR'      => '$',
                                               'SEMICOLON'   => ';',
                                               'COLON'       => ':',
                                               'DOUBLECOLON' => '::',
                                               'AT'          => '@',
                                               'HATCH'       => '#',
                                               'QUOTE'       => '"',
                                               'BACKTICK'    => '`',
                                               'VERT'        => '"|" modifier',
                                               'DOT'         => '.',
                                               'COMMA'       => '","',
                                               'QMARK'       => '"?"',
                                               'ID'          => 'id, name',
                                               'TEXT'        => 'text',
                                               'LDELSLASH'   => '{/..} closing tag',
                                               'LDEL'        => '{...} Smarty tag',
                                               'COMMENT'     => 'comment',
                                               'AS'          => 'as',
                                               'TO'          => 'to',
                                               'LOGOP'       => '"<", "==" ... logical operator',
                                               'TLOGOP'      => '"lt", "eq" ... logical operator; "is div by" ... if condition',
                                               'SCOND'       => '"is even" ... if condition',
    );

    /**
     * literal tag nesting level
     *
     * @var int
     */
    private $literal_cnt = 0;

    /**
     * preg token pattern for state TEXT
     *
     * @var string
     */
    private $yy_global_pattern1 = null;

    /**
     * preg token pattern for state TAG
     *
     * @var string
     */
    private $yy_global_pattern2 = null;

    /**
     * preg token pattern for state TAGBODY
     *
     * @var string
     */
    private $yy_global_pattern3 = null;

    /**
     * preg token pattern for state LITERAL
     *
     * @var string
     */
    private $yy_global_pattern4 = null;

    /**
     * preg token pattern for state DOUBLEQUOTEDSTRING
     *
     * @var null
     */
    private $yy_global_pattern5 = null;

    /**
     * preg token pattern for text
     *
     * @var null
     */
    private $yy_global_text = null;

    /**
     * preg token pattern for literal
     *
     * @var null
     */
    private $yy_global_literal = null;

    /**
     * constructor
     *
     * @param   string                             $source template source
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    public function __construct($source, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->data = $source;
        $this->dataLength = strlen($this->data);
        $this->counter = 0;
        if (preg_match('/^\xEF\xBB\xBF/i', $this->data, $match)) {
            $this->counter += strlen($match[0]);
        }
        $this->line = 1;
        $this->smarty = $compiler->template->smarty;
        $this->compiler = $compiler;
        $this->compiler->initDelimiterPreg();
        $this->smarty_token_names['LDEL'] = $this->smarty->getLeftDelimiter();
        $this->smarty_token_names['RDEL'] = $this->smarty->getRightDelimiter();
    }

    /**
     * open lexer/parser trace file
     *
     */
    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

   /**
     * replace placeholders with runtime preg  code
     *
     * @param string $preg
     *
     * @return string
     */
   public function replace($preg)
   {
        return $this->compiler->replaceDelimiter($preg);
   }

    /**
     * check if current value is an autoliteral left delimiter
     *
     * @return bool
     */
    public function isAutoLiteral()
    {
        return $this->smarty->getAutoLiteral() && isset($this->value[ $this->compiler->getLdelLength() ]) ?
            strpos(" \n\t\r", $this->value[ $this->compiler->getLdelLength() ]) !== false : false;
    }

     /*!lex2php
     %input $this->data
     %counter $this->counter
     %token $this->token
     %value $this->value
     %line $this->line
     userliteral = ~(SMARTYldel)SMARTYautoliteral\s+SMARTYliteral~
     char = ~[\S\s]~
     textdoublequoted = ~([^"\\]*?)((?:\\.[^"\\]*?)*?)(?=((SMARTYldel)SMARTYal|\$|`\$|"SMARTYliteral))~
     namespace = ~([0-9]*[a-zA-Z_]\w*)?(\\[0-9]*[a-zA-Z_]\w*)+~
     emptyjava = ~[{][}]~
     slash = ~[/]~
     ldel = ~(SMARTYldel)SMARTYal~
     rdel = ~\s*SMARTYrdel~
     nocacherdel = ~(\s+nocache)?\s*SMARTYrdel~
     smartyblockchildparent = ~[\$]smarty\.block\.(child|parent)~
     integer = ~\d+~
     hex =  ~0[xX][0-9a-fA-F]+~
     math = ~\s*([*]{1,2}|[%/^&]|[<>]{2})\s*~
     comment = ~(SMARTYldel)SMARTYal[*]~
     incdec = ~([+]|[-]){2}~
     unimath = ~\s*([+]|[-])\s*~
     openP = ~\s*[(]\s*~
     closeP = ~\s*[)]~
     openB = ~\[\s*~
     closeB = ~\s*\]~
     dollar = ~[$]~
     dot = ~[.]~
     comma = ~\s*[,]\s*~
     doublecolon = ~[:]{2}~
     colon = ~\s*[:]\s*~
     at = ~[@]~
     hatch = ~[#]~
     semicolon = ~\s*[;]\s*~
     equal = ~\s*[=]\s*~
     space = ~\s+~
     ptr = ~\s*[-][>]\s*~
     aptr = ~\s*[=][>]\s*~
     singlequotestring = ~'[^'\\]*(?:\\.[^'\\]*)*'~
     backtick = ~[`]~
     vert = ~[|][@]?~
     qmark = ~\s*[?]\s*~
     constant = ~[_]+[A-Z0-9][0-9A-Z_]*|[A-Z][0-9A-Z_]*(?![0-9A-Z_]*[a-z])~
     attr = ~\s+[0-9]*[a-zA-Z_][a-zA-Z0-9_\-:]*\s*[=]\s*~
     id = ~[0-9]*[a-zA-Z_]\w*~
     literal = ~literal~
     strip = ~strip~
     lop = ~\s*([!=][=]{1,2}|[<][=>]?|[>][=]?|[&|]{2})\s*~
     slop = ~\s+(eq|ne|neq|gt|ge|gte|lt|le|lte|mod|and|or|xor)\s+~
     tlop = ~\s+is\s+(not\s+)?(odd|even|div)\s+by\s+~
     scond = ~\s+is\s+(not\s+)?(odd|even)~
     isin = ~\s+is\s+in\s+~
     as = ~\s+as\s+~
     to = ~\s+to\s+~
     step = ~\s+step\s+~
     if = ~(if|elseif|else if|while)\s+~
     for = ~for\s+~
     makenocache = ~make_nocache\s+~
     array = ~array~
     foreach = ~foreach(?![^\s])~
     setfilter = ~setfilter\s+~
     instanceof = ~\s+instanceof\s+~
     not = ~[!]\s*|not\s+~
     typecast = ~[(](int(eger)?|bool(ean)?|float|double|real|string|binary|array|object)[)]\s*~
     double_quote = ~["]~
     */
     /*!lex2php
     %statename TEXT
     emptyjava {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     comment {
       $to = $this->dataLength;
       preg_match("/[*]{$this->compiler->getRdelPreg()}[\n]?/",$this->data,$match,PREG_OFFSET_CAPTURE,$this->counter);
        if (isset($match[0][1])) {
            $to = $match[0][1] + strlen($match[0][0]);
        } else {
            $this->compiler->trigger_template_error ("missing or misspelled comment closing tag '{$this->smarty->getRightDelimiter()}'");
        }
        $this->value = substr($this->data,$this->counter,$to-$this->counter);
        return false;
     }
     userliteral {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     ldel literal rdel {
        $this->token = Smarty_Internal_Templateparser::TP_LITERALSTART;
        $this->yypushstate(self::LITERAL);
     }
     ldel slash literal rdel {
        $this->token = Smarty_Internal_Templateparser::TP_LITERALEND;
        $this->yypushstate(self::LITERAL);
     }
     ldel {
        $this->yypushstate(self::TAG);
        return true;
     }
     char {
       if (!isset($this->yy_global_text)) {
           $this->yy_global_text = $this->replace('/(SMARTYldel)SMARTYal/isS');
       }
       $to = $this->dataLength;
       preg_match($this->yy_global_text, $this->data,$match,PREG_OFFSET_CAPTURE,$this->counter);
       if (isset($match[0][1])) {
         $to = $match[0][1];
       }
       $this->value = substr($this->data,$this->counter,$to-$this->counter);
       $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     */
     /*!lex2php
     %statename TAG
     ldel if {
        $this->token = Smarty_Internal_Templateparser::TP_LDELIF;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel for {
        $this->token = Smarty_Internal_Templateparser::TP_LDELFOR;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel foreach {
        $this->token = Smarty_Internal_Templateparser::TP_LDELFOREACH;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel setfilter {
        $this->token = Smarty_Internal_Templateparser::TP_LDELSETFILTER;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel makenocache {
        $this->token = Smarty_Internal_Templateparser::TP_LDELMAKENOCACHE;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel id nocacherdel {
        $this->yypopstate();
        $this->token = Smarty_Internal_Templateparser::TP_SIMPLETAG;
        $this->taglineno = $this->line;
     }
     ldel smartyblockchildparent rdel {
         $this->yypopstate();
         $this->token = Smarty_Internal_Templateparser::TP_SMARTYBLOCKCHILDPARENT;
         $this->taglineno = $this->line;
     }
     ldel slash id rdel {
        $this->yypopstate();
        $this->token = Smarty_Internal_Templateparser::TP_CLOSETAG;
        $this->taglineno = $this->line;
     }
     ldel dollar id nocacherdel {
        if ($this->_yy_stack[count($this->_yy_stack)-1] === self::TEXT) {
            $this->yypopstate();
            $this->token = Smarty_Internal_Templateparser::TP_SIMPELOUTPUT;
            $this->taglineno = $this->line;
        } else {
            $this->value = $this->smarty->getLeftDelimiter();
            $this->token = Smarty_Internal_Templateparser::TP_LDEL;
            $this->yybegin(self::TAGBODY);
            $this->taglineno = $this->line;
        }
     }
     ldel slash {
        $this->token = Smarty_Internal_Templateparser::TP_LDELSLASH;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     ldel {
        $this->token = Smarty_Internal_Templateparser::TP_LDEL;
        $this->yybegin(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     */
     /*!lex2php
     %statename TAGBODY
     rdel {
        $this->token = Smarty_Internal_Templateparser::TP_RDEL;
        $this->yypopstate();
     }
     ldel {
        $this->yypushstate(self::TAG);
        return true;
     }
     double_quote {
        $this->token = Smarty_Internal_Templateparser::TP_QUOTE;
        $this->yypushstate(self::DOUBLEQUOTEDSTRING);
        $this->compiler->enterDoubleQuote();
     }
     singlequotestring {
        $this->token = Smarty_Internal_Templateparser::TP_SINGLEQUOTESTRING;
     }
     dollar id {
        $this->token = Smarty_Internal_Templateparser::TP_DOLLARID;
     }
     dollar {
        $this->token = Smarty_Internal_Templateparser::TP_DOLLAR;
     }
     isin {
        $this->token = Smarty_Internal_Templateparser::TP_ISIN;
     }
     as {
        $this->token = Smarty_Internal_Templateparser::TP_AS;
     }
     to {
        $this->token = Smarty_Internal_Templateparser::TP_TO;
     }
     step {
        $this->token = Smarty_Internal_Templateparser::TP_STEP;
     }
     instanceof {
        $this->token = Smarty_Internal_Templateparser::TP_INSTANCEOF;
     }
     lop {
        $this->token = Smarty_Internal_Templateparser::TP_LOGOP;
     }
     slop {
        $this->token = Smarty_Internal_Templateparser::TP_SLOGOP;
     }
     tlop {
        $this->token = Smarty_Internal_Templateparser::TP_TLOGOP;
     }
     scond {
        $this->token = Smarty_Internal_Templateparser::TP_SINGLECOND;
     }
     not{
        $this->token = Smarty_Internal_Templateparser::TP_NOT;
     }
     typecast {
        $this->token = Smarty_Internal_Templateparser::TP_TYPECAST;
     }
     openP {
        $this->token = Smarty_Internal_Templateparser::TP_OPENP;
     }
     closeP {
        $this->token = Smarty_Internal_Templateparser::TP_CLOSEP;
     }
     openB {
        $this->token = Smarty_Internal_Templateparser::TP_OPENB;
     }
     closeB {
        $this->token = Smarty_Internal_Templateparser::TP_CLOSEB;
     }
     ptr {
        $this->token = Smarty_Internal_Templateparser::TP_PTR;
     }
     aptr {
        $this->token = Smarty_Internal_Templateparser::TP_APTR;
     }
     equal {
        $this->token = Smarty_Internal_Templateparser::TP_EQUAL;
     }
     incdec {
        $this->token = Smarty_Internal_Templateparser::TP_INCDEC;
     }
     unimath {
        $this->token = Smarty_Internal_Templateparser::TP_UNIMATH;
     }
     math {
        $this->token = Smarty_Internal_Templateparser::TP_MATH;
     }
     at {
        $this->token = Smarty_Internal_Templateparser::TP_AT;
     }
     array openP {
        $this->token = Smarty_Internal_Templateparser::TP_ARRAYOPEN;
     }
     hatch {
        $this->token = Smarty_Internal_Templateparser::TP_HATCH;
     }
     attr {
        // resolve conflicts with shorttag and right_delimiter starting with '='
        if (substr($this->data, $this->counter + strlen($this->value) - 1, $this->compiler->getRdelLength()) === $this->smarty->getRightDelimiter()) {
            preg_match('/\s+/',$this->value,$match);
            $this->value = $match[0];
            $this->token = Smarty_Internal_Templateparser::TP_SPACE;
        } else {
            $this->token = Smarty_Internal_Templateparser::TP_ATTR;
        }
     }
     namespace {
        $this->token = Smarty_Internal_Templateparser::TP_NAMESPACE;
     }
     id {
        $this->token = Smarty_Internal_Templateparser::TP_ID;
     }
     integer {
        $this->token = Smarty_Internal_Templateparser::TP_INTEGER;
     }
     backtick {
        $this->token = Smarty_Internal_Templateparser::TP_BACKTICK;
        $this->yypopstate();
     }
     vert {
        $this->token = Smarty_Internal_Templateparser::TP_VERT;
     }
     dot {
        $this->token = Smarty_Internal_Templateparser::TP_DOT;
     }
     comma {
        $this->token = Smarty_Internal_Templateparser::TP_COMMA;
     }
     semicolon {
        $this->token = Smarty_Internal_Templateparser::TP_SEMICOLON;
     }
     doublecolon {
        $this->token = Smarty_Internal_Templateparser::TP_DOUBLECOLON;
     }
     colon {
        $this->token = Smarty_Internal_Templateparser::TP_COLON;
     }
     qmark {
        $this->token = Smarty_Internal_Templateparser::TP_QMARK;
     }
     hex {
        $this->token = Smarty_Internal_Templateparser::TP_HEX;
     }
     space {
        $this->token = Smarty_Internal_Templateparser::TP_SPACE;
     }
     char {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     */

     /*!lex2php
     %statename LITERAL
     ldel literal rdel {
        $this->literal_cnt++;
        $this->token = Smarty_Internal_Templateparser::TP_LITERAL;
     }
     ldel slash literal rdel {
        if ($this->literal_cnt) {
             $this->literal_cnt--;
            $this->token = Smarty_Internal_Templateparser::TP_LITERAL;
        } else {
            $this->token = Smarty_Internal_Templateparser::TP_LITERALEND;
            $this->yypopstate();
        }
     }
     char {
       if (!isset($this->yy_global_literal)) {
           $this->yy_global_literal = $this->replace('/(SMARTYldel)SMARTYal[\/]?literalSMARTYrdel/isS');
       }
       $to = $this->dataLength;
       preg_match($this->yy_global_literal, $this->data,$match,PREG_OFFSET_CAPTURE,$this->counter);
       if (isset($match[0][1])) {
         $to = $match[0][1];
       } else {
          $this->compiler->trigger_template_error ("missing or misspelled literal closing tag");
       }
       $this->value = substr($this->data,$this->counter,$to-$this->counter);
       $this->token = Smarty_Internal_Templateparser::TP_LITERAL;
     }
     */
     /*!lex2php
     %statename DOUBLEQUOTEDSTRING
     userliteral {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     ldel literal rdel {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     ldel slash literal rdel {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     ldel slash {
        $this->yypushstate(self::TAG);
        return true;
     }
     ldel id {
        $this->yypushstate(self::TAG);
        return true;
     }
     ldel {
        $this->token = Smarty_Internal_Templateparser::TP_LDEL;
        $this->taglineno = $this->line;
        $this->yypushstate(self::TAGBODY);
     }
     double_quote {
        $this->token = Smarty_Internal_Templateparser::TP_QUOTE;
        $this->yypopstate();
     }
     backtick dollar {
        $this->token = Smarty_Internal_Templateparser::TP_BACKTICK;
        $this->value = substr($this->value,0,-1);
        $this->yypushstate(self::TAGBODY);
        $this->taglineno = $this->line;
     }
     dollar id {
        $this->token = Smarty_Internal_Templateparser::TP_DOLLARID;
     }
     dollar {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     textdoublequoted {
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
     char {
        $to = $this->dataLength;
        $this->value = substr($this->data,$this->counter,$to-$this->counter);
        $this->token = Smarty_Internal_Templateparser::TP_TEXT;
     }
    */
  }

     