<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#paste.paste_text_desc}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="js/pastetext.js"></script>
</head>
<body onresize="PasteTextDialog.resize();" style="display:none; overflow:hidden;">
	<form name="source" onsubmit="return PasteTextDialog.insert();" action="#">
		<div style="float: left" class="title">{#paste.paste_text_desc}</div>

		<div style="float: right">
			<input type="checkbox" name="linebreaks" id="linebreaks" class="wordWrapCode" checked="checked" /><label for="linebreaks">{#paste_dlg.text_linebreaks}</label>
		</div>

		<br style="clear: both" />

		<div>{#paste_dlg.text_title}</div>

		<textarea id="content" name="content" rows="15" cols="100" style="width: 100%; height: 100%; font-family: 'Courier New',Courier,mono; font-size: 12px;" dir="ltr" wrap="soft" class="mceFocus"></textarea>

		<div class="mceActionPanel">
			<input type="submit" name="insert" value="{#insert}" id="insert" />
			<input type="button" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" id="cancel" />
		</div>
	</form>
</body> 
</html>