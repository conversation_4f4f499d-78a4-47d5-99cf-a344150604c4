# 遊戲管理頁面地圖選擇下拉選單問題修復報告

## 問題描述

在遊戲管理頁面的第二分頁中，地圖選擇下拉選單存在以下問題：
- 用戶可以選擇地圖類型
- 但選擇完成後，選單會重置回預設狀態（"請選擇地圖類型"）
- 這讓使用者誤以為沒有選擇成功

## 問題分析

經過代碼分析，發現問題的根本原因是：

### 1. 自動保存機制衝突
- 當用戶選擇地圖類型時，會觸發 `change` 事件
- `change` 事件會調用 `autoSave()` 函數保存草稿
- 自動保存會將當前選擇存儲到 localStorage

### 2. 草稿恢復邏輯問題
- 在進入第二步驟時，系統會檢查是否有草稿數據
- 如果發現草稿，會嘗試恢復之前的選擇
- 但恢復邏輯沒有區分是用戶手動選擇還是系統自動恢復
- 這導致用戶的選擇被草稿恢復邏輯覆蓋

### 3. 時間延遲問題
- 草稿恢復使用了 200ms 的延遲執行
- 這個延遲可能會在用戶選擇後重置選單狀態

## 解決方案

### 1. 添加用戶選擇標記
在地圖類型選擇事件中添加標記，記錄用戶是否手動選擇了地圖類型：

```javascript
$('#wizard_g_map_type').on('change', function() {
    console.log('地圖類型選擇變更:', $(this).val());
    validateField(this);
    
    // 標記用戶已手動選擇地圖類型，避免草稿恢復覆蓋
    $(this).data('user-selected', true);
    
    autoSave();
});
```

### 2. 修改自動保存邏輯
在自動保存時記錄用戶選擇標記：

```javascript
const formData = {
    // ... 其他欄位
    g_map_type: mapValue,
    userSelectedMap: userSelected, // 記錄用戶是否手動選擇了地圖
    timestamp: Date.now()
};
```

### 3. 改進草稿恢復邏輯
只在用戶未手動選擇時才從草稿恢復：

```javascript
// 只有在用戶未手動選擇且地圖選單沒有值時，才從草稿恢復
if (!userSelected && (!mapValue || mapValue === '')) {
    const draftData = localStorage.getItem('gameManage_draft');
    if (draftData) {
        try {
            const data = JSON.parse(draftData);
            if (data.g_map_type && !data.userSelectedMap) {
                $('#wizard_g_map_type').val(data.g_map_type);
                // 不觸發 change 事件，避免標記為用戶選擇
            }
        } catch (e) {
            console.error('草稿解析失敗:', e);
        }
    }
}
```

### 4. 完善草稿恢復函數
確保正確處理用戶選擇標記的恢復：

```javascript
// 如果草稿中有用戶選擇標記，恢復該標記
if (data.userSelectedMap) {
    $('#wizard_g_map_type').data('user-selected', true);
}

// 恢復地圖類型值（無論是否為用戶選擇）
if (mapType) {
    $('#wizard_g_map_type').val(mapType);
}
```

## 修改的文件

- `templates/GameManage.tpl` - 主要的模板文件，包含所有JavaScript邏輯

## 修改的函數

1. **setupEventListeners()** - 添加用戶選擇標記
2. **goToStep()** - 改進第二步驟的草稿恢復邏輯
3. **autoSave()** - 記錄用戶選擇標記
4. **restoreDraftData()** - 完善草稿恢復邏輯

## 測試

創建了測試頁面 `test_map_selection.html` 來驗證修復效果：
- 可以模擬用戶選擇地圖類型
- 可以測試自動保存功能
- 可以測試草稿恢復邏輯
- 提供詳細的日誌輸出

## 預期效果

修復後，地圖選擇下拉選單應該：
1. 用戶選擇地圖類型後，選單會正確顯示所選擇的選項
2. 不會因為自動保存或草稿恢復而重置選單狀態
3. 保持良好的用戶體驗，讓用戶清楚知道已經成功選擇了地圖類型

## 注意事項

- 修改保持了向後兼容性
- 不影響其他功能的正常運作
- 添加了詳細的控制台日誌，便於調試
- 所有修改都在現有的代碼結構內進行，沒有破壞性變更
