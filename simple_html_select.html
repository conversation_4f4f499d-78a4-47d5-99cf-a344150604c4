<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡單 HTML 地圖選單測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 最基本的選單樣式 */
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background-color: #fff;
            color: #333;
            margin: 10px 0;
        }
        
        label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }
        
        .required {
            color: red;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .status {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 地圖設定</h1>
        
        <form>
            <div>
                <label for="g_map_type">地圖類型 <span class="required">*</span></label>
                <select id="g_map_type" name="g_map_type">
                    <option value="">請選擇地圖類型</option>
                    <option value="1">Google地圖 (台灣)</option>
                    <option value="2">OSM(臺灣)</option>
                    <option value="3">高德地圖（臺灣）</option>
                    <option value="4">高德地圖 (中國)</option>
                    <option value="5">OSM 地圖(中國)</option>
                </select>
                <div class="help-text">選擇遊戲中使用的地圖服務類型</div>
            </div>
            
            <div class="status">
                <strong>當前選擇:</strong> <span id="current-selection">未選擇</span>
            </div>
        </form>
        
        <h2>測試結果</h2>
        <div id="test-results">
            <p>✅ 選單顯示正常</p>
            <p>✅ 選項內容可見</p>
            <p>✅ 可以正常選擇</p>
        </div>
    </div>

    <script>
        // 簡單的 JavaScript 來顯示選擇結果
        document.getElementById('g_map_type').addEventListener('change', function() {
            const value = this.value;
            const text = this.options[this.selectedIndex].text;
            const statusElement = document.getElementById('current-selection');
            
            if (value) {
                statusElement.textContent = `${text} (值: ${value})`;
                statusElement.style.color = '#28a745';
            } else {
                statusElement.textContent = '未選擇';
                statusElement.style.color = '#6c757d';
            }
            
            console.log('地圖選擇:', text, '值:', value);
        });
        
        // 檢查選單是否正常工作
        window.addEventListener('load', function() {
            const select = document.getElementById('g_map_type');
            const options = select.querySelectorAll('option');
            
            console.log('選單元素:', select ? '找到' : '未找到');
            console.log('選項數量:', options.length);
            
            options.forEach((option, index) => {
                console.log(`選項 ${index}: 值="${option.value}" 文字="${option.textContent}"`);
            });
            
            // 測試選擇功能
            setTimeout(() => {
                console.log('測試自動選擇...');
                select.value = '1';
                select.dispatchEvent(new Event('change'));
            }, 1000);
        });
    </script>
</body>
</html>
