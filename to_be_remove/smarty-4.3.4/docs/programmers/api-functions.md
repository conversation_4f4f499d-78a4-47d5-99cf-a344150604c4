Smarty Class Methods {#api.functions}
====================

## Table of contents

- [addConfigDir()](./api-functions/api-add-config-dir.md) — add a directory to the list of directories where config files are stored
- [addPluginsDir()](./api-functions/api-add-plugins-dir.md) — add a directory to the list of directories where plugins are stored
- [addTemplateDir()](./api-functions/api-add-template-dir.md) — add a directory to the list of directories where templates are stored
- [append()](./api-functions/api-append.md) — append an element to an assigned array
- [appendByRef()](./api-functions/api-append-by-ref.md) — append values  by reference
- [assign()](./api-functions/api-assign.md) — assign variables/objects to the templates
- [assignByRef()](./api-functions/api-assign-by-ref.md) — assign values by reference
- [clearAllAssign()](./api-functions/api-clear-all-assign.md) — clears the values of all assigned variables
- [clearAllCache()](./api-functions/api-clear-all-cache.md) — clears the entire template cache
- [clearAssign()](./api-functions/api-clear-assign.md) — clears the value of an assigned variable
- [clearCache()](./api-functions/api-clear-cache.md) — clears the cache for a specific template
- [clearCompiledTemplate()](./api-functions/api-clear-compiled-tpl.md) — clears the compiled version of the specified template resource
- [clearConfig()](./api-functions/api-clear-config.md) — clears assigned config variables
- [compileAllConfig()](./api-functions/api-compile-all-config.md) — compiles all known config files
- [compileAllTemplates()](./api-functions/api-compile-all-templates.md) — compiles all known templates
- [configLoad()](./api-functions/api-config-load.md) —  loads config file  data and assigns it to the template
- [createData()](./api-functions/api-create-data.md) — creates a data object
- [createTemplate()](./api-functions/api-create-template.md) — returns a template object
- [disableSecurity()](./api-functions/api-disable-security.md) — disables template security
- [display()](./api-functions/api-display.md) — displays the template
- [enableSecurity()](./api-functions/api-enable-security.md) — enables template security
- [fetch()](./api-functions/api-fetch.md) — returns the template output
- [getCacheDir()](./api-functions/api-get-cache-dir.md) — return the directory where the rendered template's output is stored
- [getCompileDir()](./api-functions/api-get-compile-dir.md) — returns the directory where compiled templates are stored
- [getConfigDir()](./api-functions/api-get-config-dir.md) — return the directory where config files are stored
- [getConfigVars()](./api-functions/api-get-config-vars.md) — returns the given loaded config variable value
- [getPluginsDir()](./api-functions/api-get-plugins-dir.md) — return the directory where plugins are stored
- [getRegisteredObject()](./api-functions/api-get-registered-object.md) — returns a reference to a registered object
- [getTags()](./api-functions/api-get-tags.md) — return tags used by template
- [getTemplateDir()](./api-functions/api-get-template-dir.md) — return the directory where templates are stored
- [getTemplateVars()](./api-functions/api-get-template-vars.md) — returns  assigned variable value(s)
- [isCached()](./api-functions/api-is-cached.md) — returns true if there is a valid cache for this template
- [loadFilter()](./api-functions/api-load-filter.md) — load a filter plugin
- [muteExpectedErrors()](./api-functions/api-mute-expected-errors.md) — mutes expected warnings and notices deliberately generated by Smarty
- [registerCacheResource()](./api-functions/api-register-cacheresource.md) — dynamically register CacheResources
- [registerClass()](./api-functions/api-register-class.md) — register a class for use in the templates
- [registerDefaultPluginHandler()](./api-functions/api-register-default-plugin-handler.md) — register a function which gets called on undefined tags
- [registerFilter()](./api-functions/api-register-filter.md) — dynamically register filters
- [registerPlugin()](./api-functions/api-register-plugin.md) — dynamically register plugins
- [registerObject()](./api-functions/api-register-object.md) — register an object for use in the templates
- [registerResource()](./api-functions/api-register-resource.md) — dynamically register resources
- [setCacheDir()](./api-functions/api-set-cache-dir.md) — set the directory where the rendered template's output is stored
- [setCompileDir()](./api-functions/api-set-compile-dir.md) — set the directory where compiled templates are stored
- [setConfigDir()](./api-functions/api-set-config-dir.md) — set the directories where config files are stored
- [setPluginsDir()](./api-functions/api-set-plugins-dir.md) — set the directories where plugins are stored
- [setTemplateDir()](./api-functions/api-set-template-dir.md) — set the directories where templates are stored
- [templateExists()](./api-functions/api-template-exists.md) — checks whether the specified template exists
- [unregisterCacheResource()](./api-functions/api-unregister-cacheresource.md) — dynamically unregister a CacheResource plugin
- [unregisterFilter()](./api-functions/api-unregister-filter.md) — dynamically unregister a filter
- [unregisterPlugin()](./api-functions/api-unregister-plugin.md) — dynamically unregister plugins
- [unregisterObject()](./api-functions/api-unregister-object.md) — dynamically unregister an object
- [unregisterResource()](./api-functions/api-unregister-resource.md) — dynamically unregister a resource plugin
- [testInstall()](./api-functions/api-test-install.md) — checks Smarty installation

> **Note**
>
> See
> [`Changing settings by template`](./advanced-features/advanced-features-template-settings.md)
> section for how to use the functions for individual templates.
