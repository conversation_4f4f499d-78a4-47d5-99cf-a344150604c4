<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地圖選擇調試頁面</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error-box {
            background: #ffe7e7;
            border: 1px solid #ffb3b3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success-box {
            background: #e7ffe7;
            border: 1px solid #b3ffb3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>地圖選擇調試頁面</h1>
        <p>這個頁面用來測試地圖選擇功能是否正常工作。</p>
        
        <div class="status-box">
            <h3>系統狀態</h3>
            <p><strong>jQuery 版本:</strong> <span id="jquery-version">檢查中...</span></p>
            <p><strong>瀏覽器:</strong> <span id="browser-info">檢查中...</span></p>
            <p><strong>時間:</strong> <span id="current-time">檢查中...</span></p>
        </div>
        
        <div class="status-box">
            <h3>地圖選擇測試</h3>
            <label for="test-map-select">選擇地圖類型:</label>
            <select id="test-map-select">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
            
            <p><strong>當前選擇:</strong> <span id="current-selection">未選擇</span></p>
            <p><strong>選擇次數:</strong> <span id="selection-count">0</span></p>
        </div>
        
        <div class="status-box">
            <h3>測試控制</h3>
            <button onclick="testSelection('1')">測試選擇 Google地圖</button>
            <button onclick="testSelection('2')">測試選擇 OSM(臺灣)</button>
            <button onclick="testSelection('3')">測試選擇 高德地圖（臺灣）</button>
            <button onclick="resetSelection()">重置選擇</button>
            <button onclick="clearLog()">清除日誌</button>
        </div>
        
        <div id="result-box" class="status-box" style="display: none;">
            <h3>測試結果</h3>
            <p id="result-message"></p>
        </div>
        
        <div class="log" id="debug-log"></div>
    </div>

    <script>
        let selectionCount = 0;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : '';
            logElement.innerHTML += `<div style="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateStatus() {
            document.getElementById('jquery-version').textContent = $.fn.jquery || '未知';
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ')[0];
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        
        function updateSelection() {
            const select = document.getElementById('test-map-select');
            const value = select.value;
            const text = select.options[select.selectedIndex].text;
            
            document.getElementById('current-selection').textContent = value ? `${text} (值: ${value})` : '未選擇';
            document.getElementById('selection-count').textContent = selectionCount;
        }
        
        function testSelection(value) {
            log(`程式化設置地圖選擇為: ${value}`);
            const select = document.getElementById('test-map-select');
            select.value = value;
            
            // 觸發 change 事件
            $(select).trigger('change');
            
            // 檢查是否成功設置
            setTimeout(() => {
                const currentValue = select.value;
                if (currentValue === value) {
                    log(`✓ 設置成功，當前值: ${currentValue}`, 'success');
                    showResult('測試成功！地圖選擇正常工作。', 'success');
                } else {
                    log(`✗ 設置失敗，期望值: ${value}，實際值: ${currentValue}`, 'error');
                    showResult('測試失敗！地圖選擇被重置了。', 'error');
                }
            }, 100);
        }
        
        function resetSelection() {
            log('重置地圖選擇');
            document.getElementById('test-map-select').value = '';
            selectionCount = 0;
            updateSelection();
            hideResult();
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        function showResult(message, type) {
            const resultBox = document.getElementById('result-box');
            const resultMessage = document.getElementById('result-message');
            
            resultBox.className = type === 'success' ? 'success-box' : 'error-box';
            resultMessage.textContent = message;
            resultBox.style.display = 'block';
        }
        
        function hideResult() {
            document.getElementById('result-box').style.display = 'none';
        }
        
        // 初始化
        $(document).ready(function() {
            log('調試頁面初始化完成');
            updateStatus();
            updateSelection();
            
            // 監聽地圖選擇變化
            $('#test-map-select').on('change', function() {
                selectionCount++;
                const value = $(this).val();
                const text = $(this).find('option:selected').text();
                
                log(`地圖選擇變更: ${text} (值: ${value})`);
                updateSelection();
                
                // 檢查選擇是否保持
                setTimeout(() => {
                    const currentValue = $(this).val();
                    if (currentValue === value) {
                        log(`✓ 選擇保持正常: ${currentValue}`, 'success');
                    } else {
                        log(`✗ 警告：選擇被重置！期望: ${value}，實際: ${currentValue}`, 'error');
                    }
                }, 500);
            });
            
            log('事件監聽器設置完成');
        });
        
        // 每秒更新時間
        setInterval(() => {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }, 1000);
    </script>
</body>
</html>
