<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字體顯示測試</title>
    <style>
        body {
            font-family: <PERSON><PERSON>, "Microsoft JhengHei", sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        /* 不同字體大小的選單測試 */
        .select-small {
            font-size: 12px;
            padding: 6px;
            border: 1px solid #ccc;
            width: 100%;
            margin: 5px 0;
        }
        
        .select-medium {
            font-size: 14px;
            padding: 8px;
            border: 1px solid #666;
            width: 100%;
            margin: 5px 0;
        }
        
        .select-large {
            font-size: 16px;
            font-weight: bold;
            padding: 12px;
            border: 2px solid #333;
            width: 100%;
            margin: 5px 0;
            background-color: #fff;
            color: #000;
        }
        
        .select-extra-large {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border: 3px solid #000;
            width: 100%;
            margin: 5px 0;
            background-color: #fff;
            color: #000;
        }
        
        /* 不同對比度測試 */
        .high-contrast {
            background-color: #000;
            color: #fff;
            border: 2px solid #fff;
        }
        
        .medium-contrast {
            background-color: #f8f9fa;
            color: #333;
            border: 2px solid #666;
        }
        
        .low-contrast {
            background-color: #fff;
            color: #999;
            border: 1px solid #ccc;
        }
        
        label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
        
        .status {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .current-selection {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 字體顯示測試</h1>
        
        <div class="test-section">
            <h3>1. 小字體選單 (12px)</h3>
            <label>地圖類型:</label>
            <select class="select-small">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>2. 中等字體選單 (14px)</h3>
            <label>地圖類型:</label>
            <select class="select-medium">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>3. 大字體選單 (16px, 粗體)</h3>
            <label>地圖類型:</label>
            <select class="select-large">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>4. 超大字體選單 (18px, 粗體)</h3>
            <label>地圖類型:</label>
            <select class="select-extra-large">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>5. 高對比度選單 (黑底白字)</h3>
            <label>地圖類型:</label>
            <select class="select-large high-contrast">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>6. 實際使用的樣式</h3>
            <label for="wizard_g_map_type" style="font-size: 16px; font-weight: bold; color: #000;">地圖類型 <span style="color: red;">*</span></label><br>
            <select id="wizard_g_map_type" name="g_map_type" 
                    style="width: 100%; padding: 12px; border: 2px solid #333; border-radius: 4px; font-size: 16px; font-weight: bold; background-color: #fff; color: #000; font-family: Arial, 'Microsoft JhengHei', sans-serif;">
                <option value="" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">請選擇地圖類型</option>
                <option value="1" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">Google地圖 (台灣)</option>
                <option value="2" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">OSM(臺灣)</option>
                <option value="3" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">高德地圖（臺灣）</option>
                <option value="4" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">高德地圖 (中國)</option>
                <option value="5" style="font-size: 16px; font-weight: bold; color: #000; background-color: #fff;">OSM 地圖(中國)</option>
            </select><br>
            <small style="font-size: 14px; color: #666;">選擇遊戲中使用的地圖服務類型</small>
        </div>
        
        <div class="status">
            <strong>當前選擇:</strong> <span class="current-selection" id="current-selection">未選擇</span>
        </div>
        
        <div class="test-section">
            <h3>📋 測試說明</h3>
            <p><strong>請測試以上每個選單：</strong></p>
            <ul>
                <li>✅ 選單文字是否清晰可見</li>
                <li>✅ 選項內容是否完整顯示</li>
                <li>✅ 選擇功能是否正常</li>
                <li>✅ 字體大小是否合適</li>
            </ul>
            <p><strong>哪個選單的字體顯示效果最好？</strong></p>
        </div>
    </div>

    <script>
        // 為所有選單添加事件監聽器
        document.querySelectorAll('select').forEach(select => {
            select.addEventListener('change', function() {
                const value = this.value;
                const text = this.options[this.selectedIndex].text;
                const statusElement = document.getElementById('current-selection');
                
                if (value) {
                    statusElement.textContent = `${text} (值: ${value})`;
                } else {
                    statusElement.textContent = '未選擇';
                }
                
                console.log('選擇:', text, '值:', value);
            });
        });
    </script>
</body>
</html>
