# PilotRun 創新任務技術實作指南

## 📋 技術實作快速參考

**文件版本：** v1.0  
**建立日期：** 2025-01-01  
**適用範圍：** 開發團隊技術實作參考  
**相關文件：** innovative_mission_roadmap_2025.md

---

## 🗄️ 資料庫設計總覽

### 新增表格清單

```sql
-- 創新任務系統相關表格
-- 執行順序：按照依賴關係依序建立

-- 1. 任務鏈系統
CREATE TABLE mission_chain (
    chain_id INT AUTO_INCREMENT PRIMARY KEY,
    chain_name VARCHAR(100) NOT NULL COMMENT '任務鏈名稱',
    chain_description TEXT COMMENT '任務鏈描述',
    g_id INT NOT NULL COMMENT '遊戲ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (g_id) REFERENCES game(g_id)
);

CREATE TABLE mission_chain_node (
    node_id INT AUTO_INCREMENT PRIMARY KEY,
    chain_id INT NOT NULL,
    mission_id INT NOT NULL,
    node_order INT NOT NULL COMMENT '節點順序',
    unlock_condition JSON COMMENT '解鎖條件',
    story_content TEXT COMMENT '故事劇情內容',
    choice_options JSON COMMENT '選擇選項（分支劇情）',
    FOREIGN KEY (chain_id) REFERENCES mission_chain(chain_id),
    FOREIGN KEY (mission_id) REFERENCES mission(m_id)
);

CREATE TABLE user_chain_progress (
    progress_id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(32) NOT NULL,
    chain_id INT NOT NULL,
    current_node_id INT,
    completed_nodes JSON COMMENT '已完成節點列表',
    story_choices JSON COMMENT '玩家選擇記錄',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chain_id) REFERENCES mission_chain(chain_id)
);

-- 2. 協作建造系統
CREATE TABLE collaborative_mission (
    cm_id INT AUTO_INCREMENT PRIMARY KEY,
    mission_id INT NOT NULL,
    project_name VARCHAR(100) NOT NULL COMMENT '專案名稱',
    required_contributions INT NOT NULL COMMENT '需要的貢獻總數',
    current_contributions INT DEFAULT 0 COMMENT '當前貢獻數',
    contribution_types JSON COMMENT '貢獻類型定義',
    project_description TEXT COMMENT '專案描述',
    completion_reward INT DEFAULT 0 COMMENT '完成獎勵',
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    FOREIGN KEY (mission_id) REFERENCES mission(m_id)
);

CREATE TABLE collaboration_contributions (
    contrib_id INT AUTO_INCREMENT PRIMARY KEY,
    cm_id INT NOT NULL,
    user_name VARCHAR(32) NOT NULL,
    contribution_type ENUM('photo', 'text', 'location_check', 'skill_demo', 'resource') NOT NULL,
    contribution_data JSON COMMENT '貢獻內容',
    quality_score DECIMAL(3,2) DEFAULT 0 COMMENT '品質評分',
    approved_by VARCHAR(32) COMMENT '審核者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cm_id) REFERENCES collaborative_mission(cm_id)
);

-- 3. 即時聊天系統
CREATE TABLE mission_chat (
    chat_id INT AUTO_INCREMENT PRIMARY KEY,
    mission_id INT,
    user_name VARCHAR(32) NOT NULL,
    message TEXT NOT NULL,
    chat_type ENUM('team', 'alliance', 'global', 'mission_specific') DEFAULT 'mission_specific',
    message_type ENUM('text', 'image', 'system', 'achievement') DEFAULT 'text',
    reply_to_chat_id INT NULL COMMENT '回覆的訊息ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_mission_time (mission_id, created_at),
    INDEX idx_chat_type (chat_type, created_at)
);

-- 4. 技能挑戰系統
CREATE TABLE skill_challenge (
    sc_id INT AUTO_INCREMENT PRIMARY KEY,
    mission_id INT NOT NULL,
    skill_category ENUM('photography', 'cooking', 'performance', 'crafting', 'knowledge', 'fitness') NOT NULL,
    challenge_description TEXT NOT NULL,
    evaluation_criteria JSON COMMENT '評分標準',
    min_score DECIMAL(3,2) DEFAULT 0 COMMENT '最低通過分數',
    submission_deadline DATETIME,
    voting_deadline DATETIME,
    max_submissions INT DEFAULT 0 COMMENT '最大提交數量',
    FOREIGN KEY (mission_id) REFERENCES mission(m_id)
);

CREATE TABLE skill_submissions (
    submission_id INT AUTO_INCREMENT PRIMARY KEY,
    sc_id INT NOT NULL,
    user_name VARCHAR(32) NOT NULL,
    submission_type ENUM('photo', 'video', 'audio', 'text', 'file') NOT NULL,
    submission_url VARCHAR(255),
    submission_description TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_score DECIMAL(3,2) DEFAULT 0,
    vote_count INT DEFAULT 0,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    FOREIGN KEY (sc_id) REFERENCES skill_challenge(sc_id)
);

-- 5. 隨機事件系統
CREATE TABLE random_event_mission (
    event_id INT AUTO_INCREMENT PRIMARY KEY,
    mission_id INT NOT NULL,
    event_name VARCHAR(100) NOT NULL,
    trigger_conditions JSON COMMENT '觸發條件',
    trigger_probability DECIMAL(5,2) DEFAULT 0 COMMENT '觸發機率(0-100)',
    duration INT DEFAULT 3600 COMMENT '任務持續時間(秒)',
    max_participants INT DEFAULT 0 COMMENT '最大參與人數(0=無限制)',
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.0 COMMENT '獎勵倍數',
    cooldown_period INT DEFAULT 86400 COMMENT '冷卻時間(秒)',
    FOREIGN KEY (mission_id) REFERENCES mission(m_id)
);
```

### 既有表格欄位擴充

```sql
-- mission 表格擴充
ALTER TABLE mission ADD COLUMN m_mission_type_extended INT DEFAULT 0 COMMENT '擴充任務類型(6-11)';
ALTER TABLE mission ADD COLUMN m_time_limit INT DEFAULT 0 COMMENT '時間限制(秒)';
ALTER TABLE mission ADD COLUMN m_simultaneous_limit INT DEFAULT 0 COMMENT '同時參與人數限制';
ALTER TABLE mission ADD COLUMN m_ar_enabled BOOLEAN DEFAULT FALSE COMMENT '是否啟用AR功能';
ALTER TABLE mission ADD COLUMN m_ar_object_url VARCHAR(255) COMMENT 'AR物件資源URL';
ALTER TABLE mission ADD COLUMN m_qr_code_data TEXT COMMENT 'QR Code資料';

-- game 表格擴充（如果需要）
ALTER TABLE game ADD COLUMN g_enable_innovation_missions BOOLEAN DEFAULT FALSE COMMENT '是否啟用創新任務';
ALTER TABLE game ADD COLUMN g_max_chain_length INT DEFAULT 10 COMMENT '最大任務鏈長度';
```

---

## 🔧 API 端點設計

### 任務鏈相關 API

```php
// api/API_MissionChain.php
<?php
switch($action) {
    case 'GetChainList':
        // 獲取遊戲的任務鏈列表
        break;
    
    case 'GetChainProgress':
        // 獲取玩家的任務鏈進度
        break;
    
    case 'UnlockNextNode':
        // 解鎖下一個任務節點
        break;
    
    case 'MakeStoryChoice':
        // 做出故事選擇，影響後續任務路線
        break;
    
    case 'GetStoryContent':
        // 獲取故事內容
        break;
}
?>
```

### 協作建造 API

```php
// api/API_Collaboration.php
<?php
switch($action) {
    case 'GetProjectList':
        // 獲取協作專案列表
        break;
    
    case 'SubmitContribution':
        // 提交貢獻內容
        break;
    
    case 'GetProjectProgress':
        // 獲取專案進度
        break;
    
    case 'RateContribution':
        // 評分他人的貢獻
        break;
    
    case 'GetContributionList':
        // 獲取專案的所有貢獻
        break;
}
?>
```

### 即時聊天 API

```php
// api/API_Chat.php
<?php
switch($action) {
    case 'SendMessage':
        // 發送聊天訊息
        break;
    
    case 'GetChatHistory':
        // 獲取聊天記錄
        break;
    
    case 'GetOnlineUsers':
        // 獲取線上用戶列表
        break;
    
    case 'MarkAsRead':
        // 標記訊息為已讀
        break;
    
    case 'GetUnreadCount':
        // 獲取未讀訊息數量
        break;
}
?>
```

---

## 🎨 前端介面組件

### 任務鏈進度組件

```javascript
// js/mission-chain.js
class MissionChainViewer {
    constructor(containerId, chainId) {
        this.containerId = containerId;
        this.chainId = chainId;
        this.currentNode = null;
        this.completedNodes = [];
    }
    
    renderChainProgress() {
        // 渲染任務鏈進度視圖
        // 顯示已完成、當前、未解鎖的任務節點
    }
    
    showStoryContent(nodeId) {
        // 顯示故事內容
        // 支援文字、圖片、音效等多媒體內容
    }
    
    handleStoryChoice(choiceId) {
        // 處理玩家的故事選擇
        // 更新任務路線和解鎖條件
    }
}
```

### 協作進度組件

```javascript
// js/collaboration.js
class CollaborationDashboard {
    constructor(containerId, projectId) {
        this.containerId = containerId;
        this.projectId = projectId;
        this.progressData = null;
    }
    
    renderProgressBar() {
        // 渲染專案進度條
        // 顯示各階段完成情況
    }
    
    renderContributionGrid() {
        // 渲染貢獻內容網格
        // 支援照片、文字、技能展示等
    }
    
    handleContributionSubmit() {
        // 處理貢獻提交
        // 支援多種媒體類型上傳
    }
}
```

### 即時聊天組件

```javascript
// js/real-time-chat.js
class RealTimeChat {
    constructor(containerId, chatType, roomId) {
        this.containerId = containerId;
        this.chatType = chatType;
        this.roomId = roomId;
        this.websocket = null;
    }
    
    initWebSocket() {
        // 初始化 WebSocket 連線
        // 處理連線、斷線、重連邏輯
    }
    
    sendMessage(message, type = 'text') {
        // 發送訊息
        // 支援文字、圖片、位置、成就分享
    }
    
    renderMessage(messageData) {
        // 渲染單則訊息
        // 支援不同訊息類型的顯示
    }
    
    handleTypingIndicator() {
        // 處理正在輸入指示器
    }
}
```

---

## 🏗️ 核心服務類別

### 任務鏈服務

```php
// inc/ChainService.php
<?php
class ChainService {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * 檢查任務鏈解鎖條件
     */
    public function checkUnlockCondition($userId, $chainId, $nodeId) {
        // 檢查玩家是否滿足解鎖條件
        // 支援複雜的條件邏輯（AND、OR、自定義函數）
    }
    
    /**
     * 處理故事選擇
     */
    public function processStoryChoice($userId, $nodeId, $choiceId) {
        // 記錄玩家選擇
        // 更新任務鏈進度
        // 解鎖對應的後續節點
    }
    
    /**
     * 獲取個人化故事內容
     */
    public function getPersonalizedStory($userId, $nodeId) {
        // 根據玩家的歷史選擇定制故事內容
        // 支援變數替換和動態內容生成
    }
}
?>
```

### 協作管理服務

```php
// inc/CollaborationService.php
<?php
class CollaborationService {
    private $db;
    
    /**
     * 提交協作貢獻
     */
    public function submitContribution($projectId, $userId, $contributionData) {
        // 驗證貢獻內容
        // 更新專案進度
        // 觸發完成檢查
    }
    
    /**
     * 計算貢獻品質分數
     */
    public function calculateQualityScore($contributionId) {
        // 基於社群評分計算品質分數
        // 考慮評分者的信譽度
        // 防止惡意評分
    }
    
    /**
     * 檢查專案完成狀態
     */
    public function checkProjectCompletion($projectId) {
        // 檢查所有里程碑是否達成
        // 計算最終獎勵分配
        // 觸發完成慶祝效果
    }
}
?>
```

### 即時事件服務

```php
// inc/EventService.php
<?php
class EventService {
    private $db;
    
    /**
     * 檢查隨機事件觸發
     */
    public function checkEventTrigger($userId, $location, $context) {
        // 基於條件檢查是否觸發隨機事件
        // 考慮機率、冷卻時間、用戶行為
    }
    
    /**
     * 觸發隨機事件
     */
    public function triggerRandomEvent($eventId, $location) {
        // 創建事件實例
        // 通知附近玩家
        // 開始計時器
    }
    
    /**
     * 發送推播通知
     */
    public function sendEventNotification($eventId, $targetUsers) {
        // 發送 push notification
        // 支援個人化內容
        // 追蹤通知效果
    }
}
?>
```

---

## 🔄 WebSocket 實時通訊

### WebSocket 伺服器設置

```php
// websocket/ChatServer.php
<?php
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;

class ChatServer implements MessageComponentInterface {
    protected $clients;
    protected $rooms;
    
    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->rooms = [];
    }
    
    public function onOpen(ConnectionInterface $conn) {
        // 新連線建立
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId})\n";
    }
    
    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);
        
        switch($data['type']) {
            case 'join_room':
                $this->joinRoom($from, $data['room_id']);
                break;
            case 'send_message':
                $this->broadcastMessage($data['room_id'], $data['message'], $from);
                break;
            case 'typing_start':
                $this->broadcastTyping($data['room_id'], $data['user_name'], true);
                break;
        }
    }
    
    private function joinRoom($conn, $roomId) {
        if (!isset($this->rooms[$roomId])) {
            $this->rooms[$roomId] = new \SplObjectStorage;
        }
        $this->rooms[$roomId]->attach($conn);
    }
    
    private function broadcastMessage($roomId, $message, $sender) {
        if (isset($this->rooms[$roomId])) {
            foreach ($this->rooms[$roomId] as $client) {
                if ($client !== $sender) {
                    $client->send(json_encode([
                        'type' => 'new_message',
                        'message' => $message,
                        'timestamp' => time()
                    ]));
                }
            }
        }
    }
}
?>
```

### 前端 WebSocket 客戶端

```javascript
// js/websocket-client.js
class WebSocketClient {
    constructor(serverUrl) {
        this.serverUrl = serverUrl;
        this.socket = null;
        this.reconnectInterval = 5000;
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
    }
    
    connect() {
        try {
            this.socket = new WebSocket(this.serverUrl);
            
            this.socket.onopen = (event) => {
                console.log('WebSocket connected');
                this.reconnectAttempts = 0;
                this.onConnected();
            };
            
            this.socket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            };
            
            this.socket.onclose = (event) => {
                console.log('WebSocket disconnected');
                this.attemptReconnect();
            };
            
            this.socket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        } catch (error) {
            console.error('Failed to connect:', error);
            this.attemptReconnect();
        }
    }
    
    sendMessage(type, data) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify({
                type: type,
                ...data,
                timestamp: Date.now()
            }));
        }
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, this.reconnectInterval);
        }
    }
}
```

---

## 📱 推播通知系統

### 推播通知服務

```php
// inc/PushNotificationService.php
<?php
class PushNotificationService {
    private $firebaseKey;
    
    public function __construct($firebaseServerKey) {
        $this->firebaseKey = $firebaseServerKey;
    }
    
    /**
     * 發送任務相關通知
     */
    public function sendMissionNotification($userTokens, $title, $body, $data = []) {
        $notification = [
            'title' => $title,
            'body' => $body,
            'icon' => '/images/logo.png',
            'click_action' => 'FCM_PLUGIN_ACTIVITY'
        ];
        
        $message = [
            'registration_ids' => $userTokens,
            'notification' => $notification,
            'data' => $data
        ];
        
        return $this->sendToFirebase($message);
    }
    
    /**
     * 發送即時事件通知
     */
    public function sendEventNotification($location, $eventData) {
        // 查找附近的活躍用戶
        $nearbyUsers = $this->getNearbyActiveUsers($location, 1000); // 1km 範圍
        
        if (!empty($nearbyUsers)) {
            $this->sendMissionNotification(
                $nearbyUsers,
                '🎉 附近有突發事件！',
                $eventData['description'],
                ['event_id' => $eventData['event_id']]
            );
        }
    }
    
    private function sendToFirebase($message) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: key=' . $this->firebaseKey,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        return json_decode($result, true);
    }
}
?>
```

---

## 🛡️ 安全性與權限控制

### 權限檢查中介軟體

```php
// inc/PermissionMiddleware.php
<?php
class PermissionMiddleware {
    private $db;
    
    /**
     * 檢查創新任務權限
     */
    public function checkInnovationMissionAccess($userId, $missionId, $action) {
        // 檢查用戶是否有權限執行特定動作
        $permissions = [
            'view_chain' => ['user', 'admin'],
            'submit_contribution' => ['team_member', 'admin'],
            'rate_submission' => ['participant', 'admin'],
            'moderate_chat' => ['moderator', 'admin']
        ];
        
        $userRole = $this->getUserRole($userId, $missionId);
        return in_array($userRole, $permissions[$action] ?? []);
    }
    
    /**
     * 檢查協作專案權限
     */
    public function checkCollaborationAccess($userId, $projectId, $action) {
        // 檢查協作專案的參與權限
        // 考慮專案狀態、用戶角色、貢獻歷史
    }
    
    /**
     * 檢查聊天室權限
     */
    public function checkChatAccess($userId, $roomId, $roomType) {
        // 檢查聊天室存取權限
        // 根據聊天室類型應用不同規則
    }
}
?>
```

### 資料驗證與清理

```php
// inc/DataValidator.php
<?php
class DataValidator {
    /**
     * 驗證任務鏈資料
     */
    public function validateChainData($chainData) {
        $errors = [];
        
        if (empty($chainData['chain_name'])) {
            $errors[] = '任務鏈名稱不能為空';
        }
        
        if (!is_array($chainData['nodes']) || empty($chainData['nodes'])) {
            $errors[] = '任務鏈必須包含至少一個節點';
        }
        
        foreach ($chainData['nodes'] as $node) {
            if (empty($node['mission_id'])) {
                $errors[] = '節點必須關聯到有效的任務';
            }
        }
        
        return $errors;
    }
    
    /**
     * 清理用戶輸入
     */
    public function sanitizeUserInput($input, $type = 'text') {
        switch ($type) {
            case 'html':
                return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
            case 'sql':
                return mysqli_real_escape_string($this->db->getConnection(), $input);
            case 'json':
                return json_encode($input, JSON_UNESCAPED_UNICODE);
            default:
                return filter_var($input, FILTER_SANITIZE_STRING);
        }
    }
}
?>
```

---

## 📊 效能監控與優化

### 效能監控服務

```php
// inc/PerformanceMonitor.php
<?php
class PerformanceMonitor {
    private $startTime;
    private $memoryStart;
    
    public function startMonitoring() {
        $this->startTime = microtime(true);
        $this->memoryStart = memory_get_usage();
    }
    
    public function logPerformance($operation, $details = []) {
        $executionTime = microtime(true) - $this->startTime;
        $memoryUsage = memory_get_usage() - $this->memoryStart;
        
        $logData = [
            'operation' => $operation,
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'timestamp' => time(),
            'details' => $details
        ];
        
        // 記錄到效能日誌
        error_log('[PERFORMANCE] ' . json_encode($logData), 3, 'logs/performance.log');
        
        // 如果執行時間過長，發送警告
        if ($executionTime > 5.0) {
            $this->sendPerformanceAlert($logData);
        }
    }
    
    private function sendPerformanceAlert($logData) {
        // 發送效能警告到監控系統
        // 可整合 Slack、Email 或其他通知服務
    }
}
?>
```

### 快取策略

```php
// inc/CacheManager.php
<?php
class CacheManager {
    private $redis;
    
    public function __construct($redisConnection = null) {
        $this->redis = $redisConnection ?: new Redis();
    }
    
    /**
     * 快取任務鏈資料
     */
    public function cacheChainData($chainId, $data, $ttl = 3600) {
        $key = "mission_chain:{$chainId}";
        $this->redis->setex($key, $ttl, json_encode($data));
    }
    
    /**
     * 獲取快取的任務鏈資料
     */
    public function getCachedChainData($chainId) {
        $key = "mission_chain:{$chainId}";
        $data = $this->redis->get($key);
        return $data ? json_decode($data, true) : null;
    }
    
    /**
     * 快取協作專案進度
     */
    public function cacheProjectProgress($projectId, $progress) {
        $key = "project_progress:{$projectId}";
        $this->redis->setex($key, 300, json_encode($progress)); // 5分鐘快取
    }
    
    /**
     * 清除相關快取
     */
    public function clearRelatedCache($type, $id) {
        $patterns = [
            'chain' => "mission_chain:{$id}*",
            'project' => "project_progress:{$id}*",
            'user' => "user_data:{$id}*"
        ];
        
        if (isset($patterns[$type])) {
            $keys = $this->redis->keys($patterns[$type]);
            if (!empty($keys)) {
                $this->redis->del($keys);
            }
        }
    }
}
?>
```

---

## 🧪 測試策略與工具

### 單元測試範例

```php
// tests/ChainServiceTest.php
<?php
use PHPUnit\Framework\TestCase;

class ChainServiceTest extends TestCase {
    private $chainService;
    private $mockDb;
    
    protected function setUp(): void {
        $this->mockDb = $this->createMock(Database::class);
        $this->chainService = new ChainService($this->mockDb);
    }
    
    public function testCheckUnlockCondition() {
        // 測試任務鏈解鎖條件檢查
        $this->mockDb->method('fetch_array_all')
                    ->willReturn([['m_id' => 1, 'd_status' => 1]]);
        
        $result = $this->chainService->checkUnlockCondition('testuser', 1, 2);
        $this->assertTrue($result);
    }
    
    public function testProcessStoryChoice() {
        // 測試故事選擇處理
        $this->mockDb->expects($this->once())
                    ->method('query_update')
                    ->willReturn(true);
        
        $result = $this->chainService->processStoryChoice('testuser', 1, 'choice_a');
        $this->assertTrue($result);
    }
}
?>
```

### API 整合測試

```php
// tests/ApiIntegrationTest.php
<?php
class ApiIntegrationTest extends TestCase {
    private $apiUrl = 'http://localhost/api/';
    
    public function testChainApiEndpoints() {
        // 測試任務鏈 API
        $response = $this->makeApiCall('API_MissionChain.php', [
            'action' => 'GetChainList',
            'g_id' => 1
        ]);
        
        $this->assertEquals(200, $response['status']);
        $this->assertArrayHasKey('chain_list', $response['data']);
    }
    
    public function testCollaborationApi() {
        // 測試協作 API
        $response = $this->makeApiCall('API_Collaboration.php', [
            'action' => 'GetProjectList',
            'user_name' => 'testuser'
        ]);
        
        $this->assertEquals(200, $response['status']);
    }
    
    private function makeApiCall($endpoint, $params) {
        $url = $this->apiUrl . $endpoint;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'status' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
}
?>
```

---

## 📝 部署檢查清單

### 資料庫部署

- [ ] 備份現有資料庫
- [ ] 執行新表格建立腳本
- [ ] 執行既有表格修改腳本
- [ ] 驗證外鍵約束正確設置
- [ ] 建立必要的索引
- [ ] 測試資料庫連線和權限

### 應用程式部署

- [ ] 上傳新的 PHP 檔案
- [ ] 更新 inc/ 目錄下的服務類別
- [ ] 部署新的 API 端點
- [ ] 更新前端 JavaScript 檔案
- [ ] 設置 WebSocket 伺服器
- [ ] 配置推播通知服務

### 安全性檢查

- [ ] 檢查 API 權限控制
- [ ] 驗證輸入資料清理
- [ ] 測試 SQL 注入防護
- [ ] 檢查檔案上傳安全性
- [ ] 驗證 HTTPS 證書配置

### 效能優化

- [ ] 配置 Redis 快取
- [ ] 設置資料庫查詢優化
- [ ] 啟用 Gzip 壓縮
- [ ] 配置 CDN（如適用）
- [ ] 測試高並發負載

### 監控設置

- [ ] 配置效能監控
- [ ] 設置錯誤日誌追蹤
- [ ] 建立健康檢查端點
- [ ] 配置通知警告系統
- [ ] 設置備份自動化

---

**技術實作指南完成 - 準備開始創新開發！** 🚀