# PilotRun 專案規則與慣例

## Smarty 3 使用規則與最佳實踐
1. **模板繼承與組織**：
   - 使用模板繼承來組織模板代碼，避免重複代碼
   - 主要頁面結構應放在佈局模板中（如 layout.tpl）
   - 使用 `{extends file='layout.tpl'}` 來繼承佈局模板
   - 使用 `{block name='content'}...{/block}` 來定義可被子模板覆蓋的區域
   - 將共用的頁面元素（如頁頭、頁尾、選單）放在獨立的模板文件中
   - 使用 `{include file='header.tpl'}` 來包含共用元素

2. **安全性處理**：
   - 所有輸出變量必須使用 escape 修飾器：`{$var|escape}`
   - 為未定義變量提供預設值：`{$var|default:''|escape}`
   - 在訪問陣列索引前檢查其是否存在：`{if isset($array.index)}`
   - 對於 HTML 內容使用適當的 escape 類型：`{$var|escape:'html'}`
   - 對於 JavaScript 內容使用：`{$var|escape:'javascript'}`
   - 對於 URL 參數使用：`{$var|escape:'url'}`

3. **變量修飾器與格式化**：
   - 優先使用內聯變量修飾器：`{$var->escape:'html'}`
   - 也可以使用傳統的管道符號：`{$var|escape:'html'}`
   - 常用安全性修飾器：
     * `escape:'html'` - HTML 內容
     * `escape:'javascript'` - JavaScript 內容
     * `escape:'url'` - URL 參數
     * `escape:'mail'` - 電子郵件地址
   - 常用格式化修飾器：
     * `date_format:'%Y-%m-%d'` - 日期格式化
     * `truncate:80:'...'` - 文字截斷
     * `nl2br` - 換行轉換
     * `string_format:'%.2f'` - 數字格式化
   - 組合使用修飾器：`{$var|default:''|escape:'html'}`
   - 使用修飾器參數：`{$var|replace:'old':'new'}`

4. **條件語句與循環**：
   - 優先使用簡化的條件語句：`{$var ?: 'default'}`
   - 使用 isset 檢查：`{if isset($var)}{$var}{else}default{/if}`
   - 使用比較運算：`{if $var > 0 && $var < 100}`
   - 使用 foreach 循環：
     * 基本用法：`{foreach from=$array item=row}`
     * 帶索引：`{foreach from=$array key=k item=row}`
     * 帶迭代資訊：`{foreach from=$array item=row name=myloop}`

5. **緩存管理**：
  - 系統已啟用緩存，緩存生命週期為1小時
  - 緩存控制：
    * `{nocache}...{/nocache}` - 標記不緩存的內容
    * `{$smarty.now}` - 獲取當前時間（不會被緩存）
    * `{$var nocache}` - 單個變量不緩存
  - 緩存標識：
    * 使用 `{$smarty.get.param|default:''|escape}` 作為緩存ID
    * 使用 `{$user_id|default:''|escape}` 作為緩存ID
  - 緩存清理：
    * 使用 `$smarty->clearCache()` 清理所有緩存
    * 使用 `$smarty->clearCache(null, $cache_id)` 清理特定緩存
  - 緩存策略：
    * 動態內容使用 nocache
    * 靜態內容啟用緩存
    * 用戶相關內容使用用戶ID作為緩存標識
     * 帶迭代資訊：`{foreach from=$array item=row name=myloop}`
   - 使用 section 循環（需要時）：
     * 基本用法：`{section name=i loop=$array}`
     * 帶步進：`{section name=i loop=$array step=2}`
   - 使用循環變量：
     * `$smarty.foreach.myloop.index` - 當前索引
     * `$smarty.foreach.myloop.iteration` - 當前迭代次數
     * `$smarty.foreach.myloop.first` - 是否第一次迭代
     * `$smarty.foreach.myloop.last` - 是否最後一次迭代
   - 也可以使用傳統的條件語句：`{if $var}{$var}{else}default{/if}`

4. **緩存設置**：
   - 系統已啟用緩存，緩存生命週期為1小時
   - 可以在模板中使用 `{nocache}...{/nocache}` 來標記不緩存的內容
   - 可以在模板中使用 `{$smarty.now}` 來獲取當前時間，這個變量不會被緩存

5. **自動轉義**：
   - 系統已啟用自動轉義，所有輸出都會被自動轉義
   - 可以使用 `{$var nofilter}` 來禁用特定變量的自動轉義
   - 注意：禁用自動轉義可能導致XSS攻擊，請謹慎使用

6. **配置變量**：
   - 可以使用 `{#var#}` 來訪問配置變量
   - 也可以使用 `{$smarty.config.var}` 來訪問配置變量

## 命名慣例
1. **PHP 文件**：
   - 類文件使用 PascalCase（如 `UserManage.php`）
   - 功能文件使用 camelCase（如 `checkLogin.php`）
   - 包含文件使用 snake_case（如 `function_credits.php`）

2. **資料庫**：
   - 表名使用小寫，單數形式（如 `user`, `game`, `mission`）
   - 欄位名使用前綴表示所屬表（如 `g_id`, `m_id`, `u_id`）
   - 主鍵通常命名為 `Id` 或表名前綴加 `_id`（如 `g_id`）

3. **變數**：
   - PHP 變數使用 camelCase（如 `$userName`, `$gameInfo`）
   - SQL 查詢結果通常存儲在 `$row`, `$list`, `$info` 等變數中

4. **函數**：
   - 函數名使用 PascalCase（如 `GetGameInfo`, `AdminGetUserInfo`）
   - 函數名通常以動詞開頭，表示操作（如 `Get`, `Add`, `Update`, `Delete`）

## 代碼組織
1. **文件結構**：
   - 核心 PHP 文件放在根目錄
   - 包含文件放在 `inc/` 目錄
   - 模板文件放在 `templates/` 目錄
   - 編譯後的模板放在 `templates_c/` 目錄
   - 上傳文件放在 `UploadManageImage/` 和 `UploadAnswerImage/` 目錄

2. **代碼組織**：
   - 通常使用 `switch-case` 結構處理不同的操作（如 `action=Add`, `action=Delete`）
   - 數據庫操作通常封裝在函數中
   - 常用功能放在 `function.php` 和其他功能文件中

## 數據庫操作
1. **查詢模式**：
   - 使用 `$db->query()` 執行 SQL 查詢
   - 使用 `$db->fetch_array()` 獲取單行結果
   - 使用 `$db->fetch_array_all()` 獲取多行結果
   - 使用 `$db->affected_rows()` 檢查影響的行數

2. **更新模式**：
   - 使用 `$db->query_update()` 更新數據
   - 使用 `$db->query_insert()` 插入數據

3. **事務處理**：
   - 較少使用事務，大多數操作是單一 SQL 語句

## 錯誤處理
1. **錯誤顯示**：
   - 使用 JavaScript alert 顯示錯誤信息（如 `echo "<script>alert('錯誤信息');history.back();</script>";`）
   - 錯誤信息通常從語言文件中獲取（如 `$lang[GameAddError_1]`）

2. **日誌記錄**：
   - 使用 `AddLog()` 函數記錄重要操作（如 `AddLog('新增遊戲', '新增了名為 '.$_POST['g_title'].' 的遊戲');`）

## 安全實踐
1. **用戶認證**：
   - 使用 `CheckUser()` 函數驗證用戶權限（如 `CheckUser('1,2,3,4');`）
   - 用戶密碼使用 MD5 雙重加密存儲（如 `MD5(MD5('password'))`）

2. **輸入驗證**：
   - 較少使用系統化的輸入驗證
   - 通常在需要時進行特定驗證（如檢查 F2 密碼格式）

## 特殊術語
1. **用戶類型**：
   - 1: 管理員
   - 2: 普通用戶
   - 3: 未知
   - 4: 區域管理員

2. **遊戲相關**：
   - `g_f2_psw`: 遊戲的 F2 密碼，用於生成遊戲參與者賬號
   - `g_team_count`: 遊戲的團隊數量
   - `g_level_type`: 遊戲的級別類型

3. **任務相關**：
   - 普通任務: 存儲在 `mission` 表中
   - 聯盟任務: 存儲在 `crowd_mission` 表中

## 常見模式
1. **遊戲創建流程**：
   - 檢查用戶權限
   - 驗證輸入數據（如 F2 密碼格式、團隊數量）
   - 檢查點數是否足夠
   - 創建遊戲記錄
   - 扣除用戶點數
   - 記錄操作日誌

2. **任務管理流程**：
   - 檢查用戶權限
   - 驗證輸入數據
   - 上傳任務圖片（如果有）
   - 創建或更新任務記錄
   - 記錄操作日誌

3. **用戶認證流程**：
   - 檢查 session 中的用戶 ID 和類型
   - 驗證用戶是否有權限執行操作
   - 如果沒有權限，顯示錯誤信息並返回

## 注意事項
1. **點數系統**：
   - 創建遊戲需要消耗用戶點數
   - 點數不足時無法創建遊戲
   - 管理員可以為用戶添加點數

2. **F2 密碼規則**：
   - F2 密碼必須是 2-3 個字母
   - F2 密碼在系統中必須唯一
   - 試用賬號的 F2 密碼為 'TS'

3. **遊戲時間限制**：
   - 遊戲開始後，某些字段無法修改（如 F2 密碼）
   - 遊戲結束後，無法修改遊戲信息

4. **文件上傳**：
   - 上傳的圖片存儲在特定目錄中
   - 圖片文件名通常包含 ID 和時間戳
   - 系統會自動生成圖片縮略圖