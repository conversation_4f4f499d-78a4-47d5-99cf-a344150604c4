<!DOCTYPE html>
<html>
<head>
    <title>座標轉換測試診斷</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>座標轉換測試診斷</h1>
    
    <div>
        <h2>1. 輸入測試座標</h2>
        <label>緯度: <input type="text" id="testLat" value="31.230416" placeholder="例: 31.230416"></label><br><br>
        <label>經度: <input type="text" id="testLng" value="121.473701" placeholder="例: 121.473701"></label><br><br>
        <button onclick="runDiagnostic()">開始診斷</button>
    </div>
    
    <div id="results" style="margin-top: 20px; font-family: monospace; white-space: pre-line;"></div>
    
    <script src="js/PRCoords.js"></script>
    <script src="js/GPSConvert.js"></script>
    
    <script>
    function log(message) {
        document.getElementById('results').innerHTML += message + '\n';
        console.log(message);
    }
    
    function runDiagnostic() {
        document.getElementById('results').innerHTML = '';
        
        var lat = parseFloat(document.getElementById('testLat').value);
        var lng = parseFloat(document.getElementById('testLng').value);
        
        log('=== 座標轉換診斷開始 ===');
        log('測試座標: ' + lat + ', ' + lng);
        log('');
        
        // 1. 檢查轉換庫
        log('1. 檢查轉換庫載入狀態:');
        log('  PRCoords 可用: ' + (typeof PRCoords !== 'undefined'));
        log('  coordtransform 可用: ' + (typeof coordtransform !== 'undefined'));
        log('  GPSConvert 可用: ' + (typeof GPSConvert !== 'undefined'));
        log('');
        
        // 2. 檢查地理位置判斷
        log('2. 地理位置判斷:');
        var isTaiwanRegion = (lat >= 21.8 && lat <= 25.3 && lng >= 119.3 && lng <= 122.0);
        var isChinaMainlandRegion = (lat >= 18 && lat <= 54 && lng >= 73 && lng <= 135 && !isTaiwanRegion);
        
        log('  是否在台灣地區: ' + isTaiwanRegion);
        log('  是否在中國大陸地區: ' + isChinaMainlandRegion);
        log('');
        
        // 3. 測試座標轉換
        log('3. 座標轉換測試:');
        
        if (isChinaMainlandRegion) {
            log('  座標在中國大陸，需要進行轉換');
            
            // 測試 PRCoords.js
            if (typeof PRCoords !== 'undefined' && typeof PRCoords.gcj_wgs === 'function') {
                try {
                    var result = PRCoords.gcj_wgs([lng, lat]);
                    if (result && Array.isArray(result) && result.length >= 2) {
                        var convertedLat = result[1];
                        var convertedLng = result[0];
                        var offsetLat = (lat - convertedLat).toFixed(6);
                        var offsetLng = (lng - convertedLng).toFixed(6);
                        
                        log('  ✅ PRCoords.js 轉換成功:');
                        log('    原始座標 (GCJ02): ' + lat + ', ' + lng);
                        log('    轉換座標 (WGS84): ' + convertedLat + ', ' + convertedLng);
                        log('    偏移量: ' + offsetLat + ', ' + offsetLng);
                        log('    偏移距離: ' + Math.sqrt(offsetLat*offsetLat + offsetLng*offsetLng).toFixed(6));
                    } else {
                        log('  ❌ PRCoords.js 轉換失敗: 返回無效結果');
                    }
                } catch (error) {
                    log('  ❌ PRCoords.js 轉換錯誤: ' + error.message);
                }
            } else {
                log('  ⚠️ PRCoords.js 不可用');
            }
            
            // 測試 coordtransform
            if (typeof coordtransform !== 'undefined') {
                try {
                    var result = coordtransform.gcj02towgs84(lng, lat);
                    if (result && Array.isArray(result) && result.length >= 2) {
                        var convertedLat = result[1];
                        var convertedLng = result[0];
                        var offsetLat = (lat - convertedLat).toFixed(6);
                        var offsetLng = (lng - convertedLng).toFixed(6);
                        
                        log('  ✅ coordtransform 轉換成功:');
                        log('    原始座標 (GCJ02): ' + lat + ', ' + lng);
                        log('    轉換座標 (WGS84): ' + convertedLat + ', ' + convertedLng);
                        log('    偏移量: ' + offsetLat + ', ' + offsetLng);
                    }
                } catch (error) {
                    log('  ❌ coordtransform 轉換錯誤: ' + error.message);
                }
            } else {
                log('  ⚠️ coordtransform 不可用');
            }
            
        } else if (isTaiwanRegion) {
            log('  座標在台灣地區，不需要轉換（台灣不使用火星座標系統）');
        } else {
            log('  座標在其他地區，不需要轉換');
        }
        
        log('');
        log('=== 診斷完成 ===');
        
        // 4. 提供解決建議
        log('');
        log('4. 問題診斷結果:');
        
        if (!isChinaMainlandRegion) {
            log('  → 您測試的座標不在中國大陸地區，系統會直接使用原始座標');
            log('  → 這是正常行為，只有中國大陸的座標才需要進行偏移轉換');
            log('  → 建議使用中國大陸的座標進行測試，例如：');
            log('     上海: 31.230416, 121.473701');
            log('     北京: 39.904200, 116.407396');
            log('     深圳: 22.547, 114.085947');
        } else {
            if (typeof PRCoords === 'undefined' && typeof coordtransform === 'undefined') {
                log('  → 座標轉換庫沒有載入，請檢查：');
                log('     1. PRCoords.js 是否正確載入');
                log('     2. GPSConvert.js 是否正確載入');
                log('     3. 網路連線是否正常');
            } else {
                log('  → 座標轉換應該正常工作');
                log('  → 如果在實際系統中沒有轉換，請檢查瀏覽器控制台的錯誤訊息');
            }
        }
    }
    
    // 頁面載入時自動執行一次診斷
    window.onload = function() {
        runDiagnostic();
    };
    </script>
</body>
</html>