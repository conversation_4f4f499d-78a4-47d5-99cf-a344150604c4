/* Reset */
.o2k7SimpleSkin table, .o2k7SimpleSkin tbody, .o2k7SimpleSkin a, .o2k7SimpleSkin img, .o2k7SimpleSkin tr, .o2k7SimpleSkin div, .o2k7SimpleSkin td, .o2k7SimpleSkin iframe, .o2k7SimpleSkin span, .o2k7SimpleSkin * {border:0; margin:0; padding:0; background:transparent; white-space:nowrap; text-decoration:none; font-weight:normal; cursor:default; color:#000}

/* Containers */
.o2k7SimpleSkin {position:relative}
.o2k7SimpleSkin table.mceLayout {background:#E5EFFD; border:1px solid #ABC6DD;}
.o2k7SimpleSkin iframe {display:block; background:#FFF; border-bottom:1px solid #ABC6DD;}
.o2k7SimpleSkin .mceToolbar {height:26px;}

/* Layout */
.o2k7SimpleSkin .mceToolbar .mceToolbarStart span {display:block; background:url(img/button_bg.png) -22px 0; width:1px; height:22px; }
.o2k7SimpleSkin .mceToolbar .mceToolbarEnd span {display:block; background:url(img/button_bg.png) -22px 0; width:1px; height:22px}
.o2k7SimpleSkin span.mceIcon, .o2k7SimpleSkin img.mceIcon {display:block; width:20px; height:20px}
.o2k7SimpleSkin .mceIcon {background:url(../../img/icons.gif) no-repeat 20px 20px}

/* Button */
.o2k7SimpleSkin .mceButton {display:block; background:url(img/button_bg.png); width:22px; height:22px}
.o2k7SimpleSkin a.mceButton span, .o2k7SimpleSkin a.mceButton img {margin:1px 0 0 1px}
.o2k7SimpleSkin a.mceButtonEnabled:hover {background-color:#B2BBD0; background-position:0 -22px}
.o2k7SimpleSkin a.mceButtonActive {background-position:0 -44px}
.o2k7SimpleSkin .mceButtonDisabled span {opacity:0.3; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=30)}

/* Separator */
.o2k7SimpleSkin .mceSeparator {display:block; background:url(img/button_bg.png) -22px 0; width:5px; height:22px}

/* Theme */
.o2k7SimpleSkin span.mce_bold {background-position:0 0}
.o2k7SimpleSkin span.mce_italic {background-position:-60px 0}
.o2k7SimpleSkin span.mce_underline {background-position:-140px 0}
.o2k7SimpleSkin span.mce_strikethrough {background-position:-120px 0}
.o2k7SimpleSkin span.mce_undo {background-position:-160px 0}
.o2k7SimpleSkin span.mce_redo {background-position:-100px 0}
.o2k7SimpleSkin span.mce_cleanup {background-position:-40px 0}
.o2k7SimpleSkin span.mce_insertunorderedlist {background-position:-20px 0}
.o2k7SimpleSkin span.mce_insertorderedlist {background-position:-80px 0}
