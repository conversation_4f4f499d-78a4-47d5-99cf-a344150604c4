#!/bin/bash

# 建立一個臨時檔案來存儲未被引用的檔案
TEMP_FILE=$(mktemp)
UNREFERENCED_FILES=$(mktemp)

# 獲取所有PHP檔案（排除to_be_remove和backup_files目錄）
find /Users/<USER>/Documents/GitHub/Pilotrun_test_Server -maxdepth 1 -name "*.php" -type f | grep -v "to_be_remove\|backup_files" > $TEMP_FILE

# 檢查每個檔案是否被引用
while IFS= read -r file; do
  filename=$(basename "$file")
  # 在整個代碼庫中搜尋檔案名稱（排除檔案本身和to_be_remove目錄）
  references=$(grep -r --include="*.php" --include="*.tpl" --include="*.html" "$filename" /Users/<USER>/Documents/GitHub/Pilotrun_test_Server | grep -v "$file\|to_be_remove\|backup_files" | wc -l)
  
  # 如果沒有引用，則添加到未引用檔案清單
  if [ "$references" -eq 0 ]; then
    echo "$file" >> $UNREFERENCED_FILES
  fi
done < $TEMP_FILE

# 輸出未被引用的檔案
echo "未被引用的檔案清單："
cat $UNREFERENCED_FILES

# 清理臨時檔案
rm $TEMP_FILE

# 保留未引用檔案清單供後續使用
echo "未引用檔案清單已保存到 $UNREFERENCED_FILES"
