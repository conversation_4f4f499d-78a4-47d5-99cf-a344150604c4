# 專案概要文件

## 專案概述
本專案為基於 PHP 開發的 LBS 社群互動平台，主要功能包含：
- 地理標記內容分享
- 任務挑戰系統
- 社群排行榜機制
- 多平台地圖整合（百度/高德/Google/OpenStreetMap）

## 核心目標
1. 建立具備遊戲化元素的社群互動平台
2. 整合多種地圖服務 API 實現位置標記功能
3. 開發可擴展的任務管理系統
4. 提供即時數據視覺化報表

## 主要功能範圍
- 使用者認證系統（登入/註冊）
- 動態訊息牆管理
- 遊戲化任務系統（含聯盟積分）
- 地圖軌跡記錄功能
- 即時小隊位置追蹤（含時間軸回顧）
- 數據統計與圖表呈現（Chart.js 升級）
- 管理後台系統
- 多語系支援架構

## 關鍵技術棧
- PHP 7.4+/8.x
- MySQL/MariaDB
- Smarty 4.3.4 模板引擎
- JavaScript/jQuery + Chart.js
- 四種地圖 API 整合（Google Maps、高德、百度、OpenStreetMap）
- 基於 PHPExcel 的報表產生
- 即時位置追蹤系統

## 利害關係人
- 平台管理員
- 一般使用者
- 第三方地圖服務供應商
- 數據分析團隊

## 版本歷史
- v1.0.0 (2023/12/01): 初始版本上線
- v1.1.0 (2024/02/15): 新增 OpenStreetMap 整合
- v1.2.0 (2024/05/20): 強化資料視覺化功能
- v1.3.0 (2025/05/29): Smarty 4.3.4 升級完成，多語系支援優化
- v1.4.0 (2025/07/01): 聯盟任務積分系統修復，圖表功能增強
- v1.5.0 (2025/07/02): 即時小隊位置系統上線，支援時間軸回顧功能