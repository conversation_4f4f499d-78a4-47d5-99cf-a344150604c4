# 產品背景 (Product Context)

## 產品存在的原因
PilotRun 應用程式旨在解決傳統實體遊戲活動管理的複雜性和局限性。在數位化轉型的時代，許多活動組織者需要一個全面的平台來管理遊戲活動，從創建到執行，再到數據分析，PilotRun 提供了這樣一個解決方案。

## 解決的問題
1. **活動管理複雜性**：傳統的活動管理通常涉及多個系統和工具，PilotRun 整合了所有必要的功能，簡化了管理流程。
2. **地理位置限制**：通過整合多種地圖服務，PilotRun 允許活動在不同地區進行，並提供精確的位置追蹤。
3. **參與者互動**：提供了一個平台，讓參與者可以互動、競爭和分享經驗。
4. **數據收集和分析**：自動收集和分析活動數據，提供有價值的洞察。
5. **多語言和多地區支援**：支援多種語言和地圖服務，使系統可以在全球範圍內使用。

## 產品願景
PilotRun 的願景是成為全球領先的遊戲活動管理平台，為活動組織者提供一個全面、易用和可靠的工具，同時為參與者提供一個豐富和互動的體驗。

## 目標市場
1. **教育機構**：學校、大學和培訓機構可以使用 PilotRun 來組織教育性遊戲和活動。
2. **企業**：公司可以使用 PilotRun 來組織團隊建設活動和企業活動。
3. **活動組織者**：專業活動組織者可以使用 PilotRun 來管理各種類型的活動。
4. **旅遊業**：旅遊公司可以使用 PilotRun 來創建互動式旅遊體驗。
5. **公共機構**：政府和公共機構可以使用 PilotRun 來組織公共活動和宣傳活動。

## 使用者體驗目標
1. **簡單易用**：系統應該直觀且易於使用，即使對於非技術使用者也是如此。
2. **靈活性**：系統應該能夠適應各種類型的活動和需求。
3. **可靠性**：系統應該穩定且可靠，確保活動順利進行。
4. **互動性**：系統應該提供豐富的互動功能，增強參與者的體驗。
5. **數據驅動**：系統應該提供有價值的數據和洞察，幫助活動組織者做出更好的決策。
6. **差異化驗證與操作體驗**（2025/4/20 新增）：針對不同用戶角色（如管理員與一般用戶）提供差異化的驗證與操作體驗。例如，遊戲管理頁針對一般用戶提供即時錯誤提示，管理員則完全不受前端驗證限制，兼顧安全與彈性。

## 產品差異化
PilotRun 與其他活動管理系統的區別在於：
1. **多地圖服務整合**：支援多種地圖服務，適應不同地區的需求。
2. **全面的活動管理**：從創建到執行，再到數據分析，提供全面的管理功能。
3. **多語言支援**：支援多種語言，使系統可以在全球範圍內使用。
4. **豐富的互動功能**：提供豐富的互動功能，增強參與者的體驗。
5. **數據分析**：提供深入的數據分析，幫助活動組織者做出更好的決策。
6. **即時團隊位置追蹤**：多地圖類型支援的即時小隊動態系統，含時間軸回顧功能。
7. **現代化技術架構**：基於 Smarty 4.x 的模板系統，Chart.js 圖表展示，提供優秀的使用者體驗。