tinyMCE.addI18n('en.searchreplace_dlg',{findwhat:"Find What",replacewith:"Replace with",direction:"Direction",up:"Up",down:"Down",mcase:"Match Case",findnext:"Find Next",allreplaced:"All occurrences of the search string were replaced.","searchnext_desc":"Find Again",notfound:"The search has been completed. The search string could not be found.","search_title":"Find","replace_title":"Find/Replace",replaceall:"Replace All",replace:"Replace"});