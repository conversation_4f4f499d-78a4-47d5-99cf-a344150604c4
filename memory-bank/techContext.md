# 技術背景 (Technical Context)

## 技術堆疊
PilotRun 測試伺服器使用以下技術堆疊：

### 伺服器環境
- **作業系統**：Linux (基於環境詳情顯示為 Linux 5.15)
- **Web 伺服器**：Apache
- **資料庫**：MySQL
- **程式語言**：PHP 7.4+ 或 PHP 8.x（基於 composer.json 顯示支援 ^7.4 || ^8.0）

### 前端技術
- **模板引擎**：Smarty 4.3.4
- **JavaScript 框架**：jQuery
- **CSS**：自定義 CSS 樣式
- **地圖 API**：百度地圖、高德地圖、Google 地圖、OpenStreetMap

### 後端技術
- **PHP**：用於伺服器端邏輯
- **MySQL**：用於資料存儲
- **文件上傳**：使用 `inc/class_upload.php` (類名 `clsUpload`) 進行處理，內部已改用 `move_uploaded_file()`。
- **圖像處理**：PHP GD 庫 (由 `clsUpload` 用於生成縮略圖)。

## 開發環境設置
開發環境需要以下設置：

1. **LAMP 環境**：
   - Linux 作業系統
   - Apache Web 伺服器
   - MySQL 資料庫
   - PHP 程式語言

2. **資料庫設置**：
   - 資料庫名稱：pilotapp
   - 使用者名稱：pilotrun
   - 密碼：pilot123
   - 主機：localhost

3. **文件目錄結構與權限**：
   - 根目錄：/data/wwwroot/test.pilotrunapp.com
   - 上傳管理圖片目錄：UploadManageImage/
   - 上傳答案圖片目錄：UploadAnswerImage/
   - **重要權限**：Web 伺服器運行用戶（如 `www-data`, `apache`）必須對 `UploadManageImage/` 和 `UploadAnswerImage/` 及其所有子目錄具有**寫入權限**，否則文件上傳將失敗。

4. **開發工具**：
   - 代碼編輯器：如 VSCode、PHPStorm 等
   - 資料庫管理工具：如 phpMyAdmin
   - 版本控制：Git

## 技術限制和約束
1. **PHP 版本**：系統支援 PHP 7.4 或以上版本，包含 PHP 8.x
2. **資料庫結構**：現有的資料庫結構可能限制某些功能的實現
3. **第三方 API**：依賴於多個地圖服務的 API，這些 API 的變更可能影響系統功能
4. **文件上傳**：
    - 文件上傳功能可能受到伺服器 PHP 配置的限制（如 `upload_max_filesize`, `post_max_size`）。
    - **伺服器目錄權限**是文件上傳成功的關鍵限制因素。

## 依賴關係
1. **Smarty 模板引擎**：用於前端頁面渲染
2. **jQuery**：用於 DOM 操作和 AJAX 請求
3. **地圖 API**：百度地圖、高德地圖、Google 地圖、OpenStreetMap
4. **PHP 擴展**：可能需要特定的 PHP 擴展，如 GD、cURL、JSON、mbstring 等

## 安全考慮
1. **資料庫安全**：
   - 防止 SQL 注入攻擊
   - 敏感資料加密存儲

2. **用戶認證和授權**：
   - 密碼加密存儲（使用 MD5 雙重加密 - **建議升級為更安全的哈希算法如 bcrypt**）
   - 基於角色的訪問控制

3. **文件上傳安全**：
   - 文件類型和大小驗證（由 `clsUpload` 處理）。
   - **確保 `move_uploaded_file` 成功執行**（`clsUpload` 已修改）。
   - 文件存儲路徑安全，**防止路徑遍歷攻擊**（`inc/upload.php` 已加入 `realpath` 處理）。
   - 對用戶上傳的文件名進行安全處理（`MissionsManage.php` 使用 `securityCheck`）。

4. **資料庫交互安全**：
   - `sql_class.php` 中的 `query_insert` 方法已修正，能正確處理數值類型，防止因類型錯誤導致的潛在問題。

5. **跨站腳本攻擊（XSS）防護**：
   - 輸入驗證和過濾。
   - 輸出編碼（Smarty 的 `escape_html` 已啟用）。

## 性能考慮
1. **資料庫優化**：
   - 適當的索引設計
   - 查詢優化

2. **文件處理**：
   - 圖片壓縮和縮略圖生成
   - 文件存儲優化

3. **頁面加載優化**：
   - CSS 和 JavaScript 壓縮
   - 圖片優化

## 部署流程
1. **代碼更新**：
   - 通過 Git 或 FTP 上傳更新的代碼

2. **資料庫更新**：
   - 執行必要的資料庫遷移腳本

3. **配置更新**：
   - 更新必要的配置文件

4. **權限檢查**：
   - **部署後務必檢查上傳目錄的權限是否正確設置**。

5. **測試**：
   - 在測試環境中驗證更新
   - 確保所有功能正常工作

6. **上線**：
   - 將更新部署到生產環境
