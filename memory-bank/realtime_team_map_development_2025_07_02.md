# 即時小隊位置系統開發記錄 - 2025年7月2日

## 📋 專案概述

開發一個即時小隊位置顯示系統，支援多種地圖類型，具備即時更新和時間軸回顧功能。

## 🆕 新開發檔案

### 1. RealTimeMap.php
**路徑**: `/RealTimeMap.php`  
**功能**: 即時小隊位置顯示系統主頁面  
**訪問**: `https://test.pilotrunapp.com/RealTimeMap.php?g_id={遊戲ID}`

#### 主要特色：
- 支援 4 種地圖類型（Google Maps、高德地圖、百度地圖、OpenStreetMap）
- 即時團隊位置顯示（每5分鐘自動更新）
- 聯盟顏色編碼（可切換顯示/隱藏）
- 時間軸回顧功能
- 播放/暫停歷史軌跡
- 響應式設計

#### 核心程式碼結構：
```php
// 不依賴外部檔案，獨立運行
$g_id = isset($_REQUEST['g_id']) ? intval($_REQUEST['g_id']) : 0;
$map_type = isset($_REQUEST["map_type"]) ? $_REQUEST["map_type"] : "google";
$show_alliance = isset($_REQUEST["show_alliance"]) ? $_REQUEST["show_alliance"] : "1";
```

#### JavaScript 功能：
- `toggleTimelineMode()` - 切換時間軸模式
- `updateTimelinePosition()` - 更新時間軸位置
- `generateTimelinePositions()` - 生成時間軸團隊位置
- `timelinePlay()` / `timelinePause()` - 播放控制

### 2. 修改的檔案

#### Chart.tpl
**路徑**: `/templates/Chart.tpl`  
**修改**: 在「回顧路徑」右側添加「小隊動態」連結  
**修改位置**: 第 666-671 行

```smarty
{if $game_info.g_map_type == '1'}
<h4>
    <a href="TrailMap.php" target="_blank" title="{$lang.PhotosStr_11}">{$lang.PhotosStr_11}</a>
    　<a href="RealTimeMap.php?g_id={$g_id}" target="_blank" title="小隊動態" style="margin-left: 20px;">小隊動態</a>
</h4>
{/if}
```

## 🔧 技術實現細節

### 地圖初始化
支援四種地圖類型的動態初始化：
1. Google Maps - 使用 Google Maps JavaScript API
2. 高德地圖 - 使用 AMap API
3. 百度地圖 - 使用 BMap API  
4. OpenStreetMap - 使用 Leaflet

### 團隊標記系統
- 動態創建團隊標記
- 依聯盟顏色編碼（8種預設顏色）
- 顯示團隊人數
- 點擊顯示詳細資訊

### 時間軸功能
- 時間範圍：2025-07-01 09:00 至 2025-07-02 18:00
- 拖拉滑桿查看任意時間點
- 自動播放功能（每秒更新）
- 團隊位置使用三角函數模擬移動

### 聯盟顏色配置
```php
$alliance_colors = [
    1 => '#FF4444', // 紅色
    2 => '#4444FF', // 藍色
    3 => '#44FF44', // 綠色
    4 => '#FFAA00', // 橙色
    5 => '#AA44FF', // 紫色
    6 => '#00AAFF', // 天藍色
    7 => '#FF44AA', // 粉紅色
    8 => '#AAFF44', // 萊姆綠
    'default' => '#888888' // 默認灰色
];
```

## 🐛 Debug 過程與解決方案

### 問題 1：HTTP 500 錯誤
**原因**: RealTimeTeamMap.php 依賴太多外部檔案  
**解決**: 創建獨立運行的 RealTimeMap.php，移除所有 require_once

### 問題 2：團隊標記不顯示
**原因**: 資料庫沒有測試數據  
**解決**: 使用示例數據直接在客戶端生成

### 問題 3：API 功能複雜度
**原因**: 多個 API 端點相互依賴  
**解決**: 簡化為客戶端直接生成時間軸數據

## 📁 檔案架構

```
/
├── RealTimeMap.php              # 主程式（最終版本）
├── templates/
│   └── Chart.tpl               # 修改：添加小隊動態連結
└── to_be_remove/debug_2025_07_02/  # 測試檔案備份
    ├── RealTimeTeamMap.php     # 原始版本（有錯誤）
    ├── SimpleMap.php           # 成功的簡化版
    └── [其他 19 個測試檔案]
```

## 🚀 使用方式

1. **即時模式**
   - 訪問：`https://test.pilotrunapp.com/RealTimeMap.php?g_id=649`
   - 自動顯示當前團隊位置
   - 每5分鐘自動更新

2. **時間軸模式**
   - 點擊「時間軸回顧」按鈕
   - 拖拉時間軸或使用播放功能
   - 查看任意時間點的團隊位置

3. **從得分曲線圖訪問**
   - 進入 Chart.php 頁面
   - 點擊「小隊動態」連結

## 💡 未來優化建議

1. **資料來源**
   - 整合真實的 user_trail_log 數據
   - 實作 WebSocket 即時更新

2. **功能擴展**
   - 添加路徑軌跡顯示
   - 團隊間距離計算
   - 匯出功能

3. **效能優化**
   - 實作數據分頁載入
   - 客戶端快取機制
   - 地圖圖層優化

## 📊 專案統計

- 開發時間：2025年7月1日 22:00 - 7月2日 10:00
- 測試檔案數：21個
- 最終保留檔案：2個（RealTimeMap.php, Chart.tpl修改）
- 支援地圖類型：4種
- 聯盟顏色：8種 + 預設色