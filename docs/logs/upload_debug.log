2025-03-14 08:14:04 - 原始 User_Logo: UploadManageImage/1/1_20140312232436_581.jpg
POST 參數: Array
(
    [UID] => 1
    [User_Name] => admin
    [Real_Name] => Administrator
    [User_Email] => <EMAIL>
    [User_Pwd] => 
    [Confirm_User_Pwd] => 
    [User_State] => 1
    [User_Logo] => UploadManageImage/1/1_20140312232436_581.jpg
)

FILES 信息: 有上傳檔案
檔案名稱: 截圖 2025-03-14 07.21.04.png
檔案大小: 2386598
檔案類型: image/png
錯誤代碼: 0
開始處理上傳檔案...
保存路徑: UploadManageImage/1
用戶ID: 1
檔案名稱: 1
上傳結果: Array
(
    [0] => Array
        (
            [name] => 截圖 2025-03-14 07.21.04.png
            [saveName] => 1_617.png
            [size] => 2 331
            [type] => image/png
            [originalHeight] => 876
            [originalWidth] => 2402
        )

)

最終 User_Logo: UploadManageImage/1/1_617.png
2025-03-14 08:14:04 - 原始 User_Logo: UploadManageImage/1/1_20140312232436_581.jpg
POST 參數: Array
(
    [UID] => 1
    [User_Name] => admin
    [Real_Name] => Administrator
    [User_Email] => <EMAIL>
    [User_Pwd] => 
    [Confirm_User_Pwd] => 
    [User_State] => 1
    [User_Logo] => UploadManageImage/1/1_20140312232436_581.jpg
)

FILES 信息: 有上傳檔案
檔案名稱: 截圖 2025-03-14 07.21.04.png
檔案大小: 2386598
檔案類型: image/png
錯誤代碼: 0
開始處理上傳檔案...
保存路徑: UploadManageImage/1
用戶ID: 1
檔案名稱: 1
上傳結果: Array
(
    [0] => Array
        (
            [name] => 截圖 2025-03-14 07.21.04.png
            [saveName] => 1_617.png
            [size] => 2 331
            [type] => image/png
            [originalHeight] => 876
            [originalWidth] => 2402
        )

)

最終 User_Logo: UploadManageImage/1/1_617.png
執行 SQL: UPDATE user SET 
                    User_Name='admin',
                    Real_Name='Administrator',
                    User_Logo='UploadManageImage/1/1_617.png',
                    User_Email='<EMAIL>',
                    User_State=1
                    WHERE Id=1
2025-03-14 08:44:17 - AddPoint 被調用
POST 參數: Array
(
    [username-to-adjust] => 1
    [Add_User_Count] => 1
)

用戶ID: 1, 點數: 1
執行 SQL: UPDATE user SET User_Lave_Count=User_Lave_Count+1 WHERE Id=1
