# Dependency Management Guide - PilotRun Platform

**Version**: 1.0  
**Date**: 2025-07-16  
**Target Audience**: Development Team  

This guide establishes best practices for managing third-party dependencies in the PilotRun Location-Based Service platform.

---

## Table of Contents

1. [Current State](#current-state)
2. [Dependency Categories](#dependency-categories)
3. [Management Strategy](#management-strategy)
4. [Security & Maintenance](#security--maintenance)
5. [Upgrade Procedures](#upgrade-procedures)
6. [Monitoring & Auditing](#monitoring--auditing)
7. [Emergency Response](#emergency-response)

---

## Current State

### 📊 Dependency Overview
- **JavaScript Libraries**: 15+ active libraries (~9MB)
- **PHP Libraries**: Smarty 4.3 + legacy components  
- **CSS Frameworks**: Custom CSS + jQuery UI themes
- **Map Services**: 4 providers (Baidu, Gaode, Google, OSM)
- **Editor Components**: TinyMCE 3.4.5 (critical security risk)

### 🚨 Critical Issues Identified
1. **jQuery 1.10.2** (2013) - Multiple XSS vulnerabilities
2. **TinyMCE 3.4.5** (2011) - Critical security flaws
3. **jQuery UI 1.11.4** (2014) - Known security issues
4. **Outdated dependencies** across the stack

---

## Dependency Categories

### 🔴 Critical Dependencies (Core Functionality)
These dependencies are essential for core platform operation:

| Library | Purpose | Version | Status | Action Required |
|---------|---------|---------|--------|-----------------|
| **Smarty** | Template Engine | 4.3.4 | ✅ Current | Monitor updates |
| **jQuery** | DOM/AJAX | 1.10.2 | ❌ Critical | **Upgrade to 3.6+** |
| **jQuery UI** | UI Components | 1.11.4 | ❌ Outdated | **Upgrade to 1.13+** |
| **Leaflet** | Maps | 1.7.1 | ✅ Good | Monitor updates |

### 🟡 Important Dependencies (Feature Enhancement)
Enhance user experience but not critical:

| Library | Purpose | Version | Status | Action Required |
|---------|---------|---------|--------|-----------------|
| **Chart.js** | Charts | CDN Latest | ✅ Current | Continue CDN usage |
| **WordCloud2** | Visualizations | Unknown | ⚠️ Check | Verify version |
| **ColorBox** | Image Gallery | 1.6.4 | ⚠️ Old | Consider replacement |

### 🟢 Optional Dependencies (Nice-to-Have)
Can be replaced or removed without major impact:

| Library | Purpose | Status | Recommendation |
|---------|---------|--------|----------------|
| **TinyMCE** | Rich Text Editor | ❌ Critical Risk | **Replace with modern editor** |
| **UEditor Mini** | Chinese Editor | ⚠️ Unknown | Evaluate necessity |
| **Live Validation** | Form Validation | ⚠️ Old | Consider modern alternatives |

---

## Management Strategy

### 🎯 Dependency Selection Criteria

#### For New Dependencies
1. **Security**: Regular security updates and active maintenance
2. **Performance**: Minimal size and fast loading
3. **Compatibility**: Works with existing stack
4. **Documentation**: Comprehensive docs and community support
5. **License**: Compatible with project licensing

#### Evaluation Checklist
```markdown
- [ ] Last update within 12 months
- [ ] Active GitHub repository (commits, issues, PRs)
- [ ] No known critical security vulnerabilities
- [ ] Size impact acceptable (<100KB uncompressed)
- [ ] Compatible with target browsers
- [ ] Clear migration path if replacement needed
```

### 📦 Preferred Hosting Strategies

#### 1. **CDN for Major Libraries** (Recommended)
```html
<!-- Preferred: Use well-maintained CDNs -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
```

**Benefits**:
- Automatic updates to patch versions
- Better caching across sites
- Reduced server load
- Built-in optimization

#### 2. **Local Hosting for Custom/Modified Libraries**
```html
<!-- For libraries with custom modifications -->
<script src="js/GPSConvert.js"></script>
<script src="js/PRCoords.js"></script>
```

**Use Cases**:
- Custom coordinate conversion libraries
- Modified third-party code
- Libraries not available on CDN

#### 3. **Package Managers for PHP**
```json
{
  "require": {
    "smarty/smarty": "^4.3",
    "monolog/monolog": "^3.0"
  }
}
```

---

## Security & Maintenance

### 🔒 Security Monitoring

#### Monthly Security Audit
```bash
# Check for known vulnerabilities
npm audit
composer audit

# Monitor for security advisories
# - GitHub Security Advisories
# - CVE Database
# - Library-specific security channels
```

#### Vulnerability Response Process
1. **Assessment** (within 24 hours)
   - Severity evaluation
   - Impact analysis
   - Exploit availability

2. **Immediate Action** (critical vulnerabilities)
   - Disable affected functionality if possible
   - Apply temporary mitigations
   - Communicate with stakeholders

3. **Permanent Fix** (within 7 days)
   - Update to patched version
   - Test thoroughly
   - Deploy to production

### 📊 Regular Maintenance Schedule

#### Weekly
- [ ] Check for critical security updates
- [ ] Monitor dependency health (GitHub activity)
- [ ] Review error logs for dependency-related issues

#### Monthly  
- [ ] Update patch versions of all dependencies
- [ ] Review dependency usage statistics
- [ ] Clean up unused dependencies

#### Quarterly
- [ ] Major version updates (with testing)
- [ ] Performance audit of all dependencies
- [ ] Evaluate new alternatives for outdated libraries
- [ ] Update this guide

---

## Upgrade Procedures

### 🚀 Safe Upgrade Process

#### Phase 1: Preparation
```bash
# 1. Backup current state
git checkout -b upgrade-jquery-$(date +%Y%m%d)
cp -r js/jquery-ui-1.11.4 js/jquery-ui-1.11.4.backup

# 2. Research breaking changes
# - Read CHANGELOG
# - Review migration guides
# - Check compatibility matrices
```

#### Phase 2: Staging Update
```bash
# 3. Install new version
wget https://code.jquery.com/ui/1.13.2/jquery-ui.min.js
# or use CDN

# 4. Update references
find templates/ -name "*.tpl" -exec sed -i 's/jquery-ui-1.11.4/jquery-ui-1.13.2/g' {} \;
```

#### Phase 3: Testing
```bash
# 5. Test critical paths
# - User login/logout
# - Map functionality  
# - Form submissions
# - Chart generation
# - File uploads

# 6. Performance testing
# - Page load times
# - JavaScript execution time
# - Memory usage
```

#### Phase 4: Deployment
```bash
# 7. Production deployment
git add .
git commit -m "Upgrade jQuery UI from 1.11.4 to 1.13.2"
# Deploy through established pipeline
```

### 🧪 Testing Protocol

#### Automated Testing (Recommended Future Enhancement)
```javascript
// Unit tests for critical functionality
describe('Map Coordinate Conversion', () => {
  it('should convert BD09 to GCJ02 correctly', () => {
    const result = bd09togcj02(116.404, 39.915);
    expect(result).toBeWithinRange(expected);
  });
});
```

#### Manual Testing Checklist
- [ ] Login/logout functionality
- [ ] Map loading and interaction (all 4 providers)
- [ ] Form validation and submission
- [ ] File upload functionality
- [ ] Chart generation and interaction
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility (Chrome, Firefox, Safari, Edge)

---

## Monitoring & Auditing

### 📈 Key Metrics

#### Performance Metrics
```javascript
// Monitor in production
window.performance.getEntriesByType('navigation')[0].loadEventEnd;
console.log('Dependencies loaded in:', loadTime, 'ms');
```

#### Health Indicators
- **Load Time**: Total JavaScript load time <2 seconds
- **Bundle Size**: Total dependencies <2MB compressed
- **Error Rate**: Dependency-related errors <0.1%
- **Update Frequency**: Critical updates applied within 7 days

#### Monthly Report Template
```markdown
## Dependency Health Report - [Month Year]

### Security Status
- Critical vulnerabilities: [count]
- Updates applied: [count]
- Pending updates: [count]

### Performance Impact
- Total bundle size: [size] (trend: ↑↓)
- Average load time: [time] (trend: ↑↓)
- Error rate: [percentage] (trend: ↑↓)

### Maintenance Actions
- Libraries updated: [list]
- Libraries removed: [list]
- New dependencies added: [list]

### Next Month Priorities
- [ ] [Action 1]
- [ ] [Action 2]
```

---

## Emergency Response

### 🚨 Critical Vulnerability Response

#### Immediate Actions (0-4 hours)
1. **Assess Impact**
   ```bash
   # Check if vulnerable version is in use
   grep -r "jquery-1.10.2" templates/
   grep -r "tinymce.3.4.5" .
   ```

2. **Temporary Mitigation**
   ```html
   <!-- Disable vulnerable functionality -->
   <!-- <script src="js/vulnerable-library.js"></script> -->
   
   <!-- Or add security headers -->
   <meta http-equiv="Content-Security-Policy" content="...">
   ```

3. **Stakeholder Communication**
   - Notify team lead
   - Assess user impact
   - Prepare communication if needed

#### Short-term Fix (4-24 hours)
1. **Apply Patch/Update**
   ```bash
   # Quick update with minimal testing
   wget [patched-version-url]
   # Test critical paths only
   # Deploy emergency fix
   ```

2. **Monitor for Issues**
   ```bash
   # Watch error logs
   tail -f /var/log/nginx/error.log | grep -i "javascript\|dependency"
   ```

#### Long-term Resolution (1-7 days)
1. **Comprehensive Testing**
2. **Full Regression Suite**
3. **Documentation Update**
4. **Process Improvement**

### 📞 Emergency Contacts

| Role | Contact | Response Time |
|------|---------|---------------|
| Lead Developer | [Contact Info] | 2 hours |
| Security Team | [Contact Info] | 1 hour |
| DevOps | [Contact Info] | 30 minutes |

---

## Best Practices Summary

### ✅ Do
- Use CDN for major, stable libraries
- Pin specific versions in production
- Test updates in staging first  
- Monitor security advisories
- Document all dependencies
- Keep upgrade logs
- Regular security audits

### ❌ Don't
- Use "latest" tags in production
- Skip testing after updates
- Ignore security warnings
- Mix multiple versions of same library
- Include unused dependencies
- Update everything at once
- Forget to update documentation

### 🎯 Immediate Priorities (Next 30 Days)

1. **🔴 Critical Security Updates**
   - [ ] Upgrade jQuery from 1.10.2 to 3.6+
   - [ ] Replace TinyMCE 3.4.5 with modern editor
   - [ ] Update jQuery UI to 1.13+

2. **🟡 Performance Optimizations**
   - [ ] Compress uncompressed JavaScript files
   - [ ] Migrate to CDN where appropriate
   - [ ] Remove unused dependencies

3. **🟢 Process Improvements**
   - [ ] Implement monthly security audits
   - [ ] Create automated testing for critical paths
   - [ ] Establish update deployment pipeline

---

**Document Maintenance**: This guide should be reviewed and updated quarterly or after major dependency changes.

**Last Updated**: 2025-07-16  
**Next Review**: 2025-10-16