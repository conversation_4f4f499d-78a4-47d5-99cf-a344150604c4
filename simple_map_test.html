<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡單地圖選單測試</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 基本選單樣式 */
        .basic-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin: 10px 0;
            background-color: #fff;
            color: #333;
        }
        
        /* 模擬原始樣式 */
        .enhanced-input {
            margin-bottom: 20px;
        }
        
        .enhanced-input select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            color: #495057;
        }
        
        /* 測試有問題的樣式 */
        .problematic-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            color: #495057;
            cursor: pointer;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzZjNzU3ZCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+');
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 12px 8px;
            padding-right: 40px;
        }
        
        .status {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>簡單地圖選單測試</h1>
        
        <h3>1. 基本選單（無特殊樣式）</h3>
        <select class="basic-select" id="basic-select">
            <option value="">請選擇地圖類型</option>
            <option value="1">Google地圖 (台灣)</option>
            <option value="2">OSM(臺灣)</option>
            <option value="3">高德地圖（臺灣）</option>
            <option value="4">高德地圖 (中國)</option>
            <option value="5">OSM 地圖(中國)</option>
        </select>
        
        <h3>2. 增強樣式選單</h3>
        <div class="enhanced-input">
            <select id="enhanced-select">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <h3>3. 有問題的樣式選單（appearance: none）</h3>
        <select class="problematic-select" id="problematic-select">
            <option value="">請選擇地圖類型</option>
            <option value="1">Google地圖 (台灣)</option>
            <option value="2">OSM(臺灣)</option>
            <option value="3">高德地圖（臺灣）</option>
            <option value="4">高德地圖 (中國)</option>
            <option value="5">OSM 地圖(中國)</option>
        </select>
        
        <h3>4. 模擬實際的向導選單</h3>
        <div class="enhanced-input">
            <select id="wizard_g_map_type" name="g_map_type">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
        </div>
        
        <div class="status">
            <strong>選擇狀態:</strong>
            <div id="selection-status">未選擇</div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateStatus() {
            const selects = ['basic-select', 'enhanced-select', 'problematic-select', 'wizard_g_map_type'];
            let status = '';
            
            selects.forEach(id => {
                const select = document.getElementById(id);
                const value = select.value;
                const text = select.options[select.selectedIndex].text;
                status += `${id}: ${value ? `${text} (${value})` : '未選擇'}<br>`;
            });
            
            document.getElementById('selection-status').innerHTML = status;
        }
        
        $(document).ready(function() {
            log('測試頁面初始化完成');
            
            // 為所有選單添加事件監聽器
            $('select').on('change', function() {
                const id = this.id;
                const value = $(this).val();
                const text = $(this).find('option:selected').text();
                
                log(`${id} 選擇變更: ${text} (值: ${value})`);
                updateStatus();
                
                // 檢查選擇是否保持
                setTimeout(() => {
                    const currentValue = $(this).val();
                    if (currentValue === value) {
                        log(`✓ ${id} 選擇保持正常`);
                    } else {
                        log(`✗ ${id} 選擇被重置！期望: ${value}, 實際: ${currentValue}`);
                    }
                }, 100);
            });
            
            updateStatus();
        });
    </script>
</body>
</html>
