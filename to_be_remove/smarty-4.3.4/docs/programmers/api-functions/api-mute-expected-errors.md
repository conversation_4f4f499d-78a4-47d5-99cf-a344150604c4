Smarty::muteExpectedErrors()

mutes expected warnings and notices deliberately generated by Smarty

Description
===========

string

muteExpectedErrors

muteExpectedErrors() registers a custom error handler using
[set\_error\_handler()](https://www.php.net/set_error_handler). The error
handler merely inspects `$errno` and `$errfile` to determine if the
given error was produced deliberately and must be ignored, or should be
passed on to the next error handler.

`Smarty::unmuteExpectedErrors()` removes the current error handler.
Please note, that if you\'ve registered any custom error handlers after
the muteExpectedErrors() call, the unmute will not remove <PERSON><PERSON>\'s
muting error handler, but the one registered last.
