# Smarty template engine
Smarty is a template engine for PHP, facilitating the separation of presentation (HTML/CSS) from application logic. 

![CI](https://github.com/smarty-php/smarty/workflows/CI/badge.svg)

## Documentation
Read the [documentation](https://smarty-php.github.io/smarty/) to find out how to use it. 

## Requirements
Smarty can be run with PHP 7.1 to PHP 8.2.

## Installation
Smarty versions 3.1.11 or later can be installed with [Composer](https://getcomposer.org/).

To get the latest stable version of Smarty use:
```bash
composer require smarty/smarty
````

More in the [Getting Started](./docs/getting-started.md) section of the docs.
