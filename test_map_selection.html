<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地圖選擇測試</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>地圖選擇下拉選單測試</h1>
        <p>這個測試頁面用來驗證地圖選擇下拉選單的問題修復。</p>
        
        <div class="form-group">
            <label for="wizard_g_map_type">地圖類型 *</label>
            <select id="wizard_g_map_type" name="g_map_type">
                <option value="">請選擇地圖類型</option>
                <option value="1">Google地圖 (台灣)</option>
                <option value="2">OSM(臺灣)</option>
                <option value="3">高德地圖（臺灣）</option>
                <option value="4">高德地圖 (中國)</option>
                <option value="5">OSM 地圖(中國)</option>
            </select>
            
            <div class="info-box">
                <strong>當前選擇：</strong>
                <span id="current-selection">未選擇</span><br>
                <strong>用戶選擇標記：</strong>
                <span id="user-selected-flag">false</span><br>
                <strong>草稿中的值：</strong>
                <span id="draft-value">無</span>
            </div>
        </div>
        
        <div class="form-group">
            <button onclick="simulateAutoSave()">模擬自動保存</button>
            <button onclick="simulateDraftRestore()">模擬草稿恢復</button>
            <button onclick="clearDraft()">清除草稿</button>
            <button onclick="clearLog()">清除日誌</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let autoSaveEnabled = true;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateDisplay() {
            const value = $('#wizard_g_map_type').val();
            const text = $('#wizard_g_map_type option:selected').text();
            const userSelected = $('#wizard_g_map_type').data('user-selected') || false;
            
            $('#current-selection').text(value ? `${text} (值: ${value})` : '未選擇');
            $('#user-selected-flag').text(userSelected.toString());
            
            // 顯示草稿中的值
            const draft = localStorage.getItem('gameManage_draft');
            if (draft) {
                try {
                    const data = JSON.parse(draft);
                    $('#draft-value').text(data.g_map_type || '無');
                } catch (e) {
                    $('#draft-value').text('解析錯誤');
                }
            } else {
                $('#draft-value').text('無');
            }
        }
        
        function autoSave() {
            if (!autoSaveEnabled) {
                log('自動保存未啟動，跳過');
                return;
            }
            
            const mapValue = $('#wizard_g_map_type').val();
            const userSelected = $('#wizard_g_map_type').data('user-selected');
            
            log(`執行自動保存 - 地圖類型值: ${mapValue}, 用戶選擇標記: ${userSelected}`);
            
            const formData = {
                g_map_type: mapValue,
                userSelectedMap: userSelected,
                timestamp: Date.now()
            };
            
            localStorage.setItem('gameManage_draft', JSON.stringify(formData));
            updateDisplay();
        }
        
        function simulateAutoSave() {
            log('手動觸發自動保存');
            autoSave();
        }
        
        function simulateDraftRestore() {
            log('模擬草稿恢復');
            const draftData = localStorage.getItem('gameManage_draft');
            if (draftData) {
                try {
                    const data = JSON.parse(draftData);
                    const userSelected = $('#wizard_g_map_type').data('user-selected');
                    
                    log(`草稿數據: g_map_type=${data.g_map_type}, userSelectedMap=${data.userSelectedMap}`);
                    log(`當前用戶選擇標記: ${userSelected}`);
                    
                    // 模擬修復後的邏輯
                    if (!userSelected && data.g_map_type && !data.userSelectedMap) {
                        log('條件符合，恢復草稿中的地圖類型');
                        $('#wizard_g_map_type').val(data.g_map_type);
                    } else if (data.userSelectedMap) {
                        log('恢復用戶選擇標記');
                        $('#wizard_g_map_type').data('user-selected', true);
                        if (data.g_map_type) {
                            $('#wizard_g_map_type').val(data.g_map_type);
                        }
                    } else {
                        log('不符合恢復條件，跳過');
                    }
                    
                    updateDisplay();
                } catch (e) {
                    log('草稿解析失敗: ' + e.message);
                }
            } else {
                log('沒有草稿數據');
            }
        }
        
        function clearDraft() {
            localStorage.removeItem('gameManage_draft');
            log('草稿已清除');
            updateDisplay();
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 設置事件監聽器
        $(document).ready(function() {
            $('#wizard_g_map_type').on('change', function() {
                const value = $(this).val();
                const text = $(this).find('option:selected').text();
                
                log(`地圖類型選擇變更: ${text} (值: ${value})`);
                
                // 標記用戶已手動選擇地圖類型
                $(this).data('user-selected', true);
                log('設置用戶選擇標記為 true');
                
                autoSave();
                updateDisplay();
            });
            
            updateDisplay();
            log('測試頁面初始化完成');
        });
    </script>
</body>
</html>
