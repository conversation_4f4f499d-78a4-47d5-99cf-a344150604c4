enableSecurity()

enables template security

Description
===========

string

enableSecurity

string

securityclass

string

enableSecurity

object

securityobject

string

enableSecurity

This enables security checking on templates. It uses the following
parameters:

-   `securityclass` is an optional parameter. It\'s the name of the
    class with defines the security policy parameters.

-   `securityobject` is an optional parameter. It\'s the object with
    defines the security policy parameters.

For the details how to setup a security policy see the
[Security](#advanced.features.security) section.

See also [`disableSecurity()`](#api.disable.security), and
[Security](#advanced.features.security).
