<!DOCTYPE html>
<html>
<head>
    <title>Chart.js 測試</title>
    <script src="js/chart.js/chart.umd.js"></script>
    <script src="js/chart.js/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <h1>Chart.js 測試頁面</h1>
    
    <div style="width: 400px; height: 300px;">
        <canvas id="testChart"></canvas>
    </div>

    <script>
        // 檢查 Chart 是否已定義
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js loaded successfully, version:', Chart.version);
            
            // 創建簡單測試圖表
            const ctx = document.getElementById('testChart');
            const testChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['A', 'B', 'C', 'D'],
                    datasets: [{
                        label: '測試數據',
                        data: [12, 19, 3, 5],
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            console.log('Test chart created successfully');
        } else {
            console.error('Chart.js not loaded');
            document.getElementById('testChart').outerHTML = '<div style="color: red;">Chart.js 加載失敗</div>';
        }
    </script>
</body>
</html>