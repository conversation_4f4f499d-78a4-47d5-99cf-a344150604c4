<?php

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
};


// line 11 "../smarty/lexer/smarty_internal_templateparser.y"

/**
* Smarty Template Parser Class
*
* This is the template parser.
* It is generated from the smarty_internal_templateparser.y file
* 
* <AUTHOR> Tews <<EMAIL>>
*/
class Smarty_Internal_Templateparser
{
// line 23 "../smarty/lexer/smarty_internal_templateparser.y"

    const ERR1 = 'Security error: Call to private object member not allowed';
    const ERR2 = 'Security error: Call to dynamic object member not allowed';

    /**
     * result status
     *
     * @var bool
     */
    public $successful = true;

    /**
     * return value
     *
     * @var mixed
     */
    public $retvalue = 0;

    /**
     * @var
     */
    public $yymajor;

    /**
     * last index of array variable
     *
     * @var mixed
     */
    public $last_index;

    /**
     * last variable name
     *
     * @var string
     */
    public $last_variable;

    /**
     * root parse tree buffer
     *
     * @var Smarty_Internal_ParseTree_Template
     */
    public $root_buffer;

    /**
     * current parse tree object
     *
     * @var Smarty_Internal_ParseTree
     */
    public $current_buffer;

    /**
     * lexer object
     *
     * @var Smarty_Internal_Templatelexer
     */
    public $lex;

    /**
     * internal error flag
     *
     * @var bool
     */
    private $internalError = false;

    /**
     * {strip} status
     *
     * @var bool
     */
    public $strip = false;
    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * template object
     *
     * @var Smarty_Internal_Template
     */
    public $template = null;

    /**
     * block nesting level
     *
     * @var int
     */
    public $block_nesting_level = 0;

    /**
     * security object
     *
     * @var Smarty_Security
     */
    public $security = null;

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_prefix = array();

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_postfix = array();

    /**
     * constructor
     *
     * @param Smarty_Internal_Templatelexer        $lex
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    public function __construct(Smarty_Internal_Templatelexer $lex, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->template = $this->compiler->template;
        $this->smarty = $this->template->smarty;
        $this->security = isset($this->smarty->security_policy) ? $this->smarty->security_policy : false;
        $this->current_buffer = $this->root_buffer = new Smarty_Internal_ParseTree_Template();
    }

     /**
     * insert PHP code in current buffer
     *
     * @param string $code
     */
    public function insertPhpCode($code)
    {
        $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Tag($this, $code));
    }

    /**
     * error rundown
     *
     */
    public function errorRunDown()
    {
        while ($this->yystack !== array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    /**
     *  merge PHP code with prefix code and return parse tree tag object
     *
     * @param string $code
     *
     * @return Smarty_Internal_ParseTree_Tag
     */
    public function mergePrefixCode($code)
    {
        $tmp = '';
        foreach ($this->compiler->prefix_code as $preCode) {
            $tmp .= $preCode;
        }
        $this->compiler->prefix_code = array();
        $tmp .= $code;
        return new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp, true));
    }


    const TP_VERT                           =  1;
    const TP_COLON                          =  2;
    const TP_TEXT                           =  3;
    const TP_STRIPON                        =  4;
    const TP_STRIPOFF                       =  5;
    const TP_LITERALSTART                   =  6;
    const TP_LITERALEND                     =  7;
    const TP_LITERAL                        =  8;
    const TP_SIMPELOUTPUT                   =  9;
    const TP_SIMPLETAG                      = 10;
    const TP_SMARTYBLOCKCHILDPARENT         = 11;
    const TP_LDEL                           = 12;
    const TP_RDEL                           = 13;
    const TP_DOLLARID                       = 14;
    const TP_EQUAL                          = 15;
    const TP_ID                             = 16;
    const TP_PTR                            = 17;
    const TP_LDELMAKENOCACHE                = 18;
    const TP_LDELIF                         = 19;
    const TP_LDELFOR                        = 20;
    const TP_SEMICOLON                      = 21;
    const TP_INCDEC                         = 22;
    const TP_TO                             = 23;
    const TP_STEP                           = 24;
    const TP_LDELFOREACH                    = 25;
    const TP_SPACE                          = 26;
    const TP_AS                             = 27;
    const TP_APTR                           = 28;
    const TP_LDELSETFILTER                  = 29;
    const TP_CLOSETAG                       = 30;
    const TP_LDELSLASH                      = 31;
    const TP_ATTR                           = 32;
    const TP_INTEGER                        = 33;
    const TP_COMMA                          = 34;
    const TP_OPENP                          = 35;
    const TP_CLOSEP                         = 36;
    const TP_MATH                           = 37;
    const TP_UNIMATH                        = 38;
    const TP_ISIN                           = 39;
    const TP_QMARK                          = 40;
    const TP_NOT                            = 41;
    const TP_TYPECAST                       = 42;
    const TP_HEX                            = 43;
    const TP_DOT                            = 44;
    const TP_INSTANCEOF                     = 45;
    const TP_SINGLEQUOTESTRING              = 46;
    const TP_DOUBLECOLON                    = 47;
    const TP_NAMESPACE                      = 48;
    const TP_AT                             = 49;
    const TP_HATCH                          = 50;
    const TP_OPENB                          = 51;
    const TP_CLOSEB                         = 52;
    const TP_DOLLAR                         = 53;
    const TP_LOGOP                          = 54;
    const TP_SLOGOP                         = 55;
    const TP_TLOGOP                         = 56;
    const TP_SINGLECOND                     = 57;
    const TP_ARRAYOPEN                      = 58;
    const TP_QUOTE                          = 59;
    const TP_BACKTICK                       = 60;
    const YY_NO_ACTION = 514;
    const YY_ACCEPT_ACTION = 513;
    const YY_ERROR_ACTION = 512;

    const YY_SZ_ACTTAB = 1997;
public static $yy_action = array(
    249,  250,  239,    1,   27,  127,  220,  184,  160,  213,
     11,   54,  278,   10,  173,   34,  108,  387,  282,  279,
    223,  321,  221,    8,  194,  387,   18,  387,   85,   41,
    387,  285,   42,   44,  264,  222,  387,  209,  387,  198,
    387,   52,    5,  307,  288,  288,  164,  283,  224,    4,
     50,  249,  250,  239,    1,  232,  131,  381,  189,  205,
    213,   11,   54,   39,   35,  243,   31,  108,   94,   17,
    381,  223,  321,  221,  439,  226,  381,   33,   49,  426,
     41,  439,   89,   42,   44,  264,  222,    9,  235,  163,
    198,  426,   52,    5,  131,  288,  212,  284,  102,  106,
      4,   50,  249,  250,  239,    1,  232,  129,  426,  189,
    347,  213,   11,   54,  175,  324,  347,  208,  108,   22,
    426,  301,  223,  321,  221,  302,  226,  135,   18,   49,
     52,   41,   26,  288,   42,   44,  264,  222,   16,  235,
    294,  198,  204,   52,    5,  170,  288,   32,   90,  267,
    268,    4,   50,  249,  250,  239,    1,   20,  129,  185,
    179,  255,  213,   11,   54,  455,  288,  192,  455,  108,
    175,  167,  455,  223,  321,  221,  439,  226,  256,   18,
     55,  292,   41,  439,  132,   42,   44,  264,  222,  427,
    235,   12,  198,  165,   52,    5,  232,  288,  288,  347,
    153,  427,    4,   50,  249,  250,  239,    1,  232,  129,
    286,  181,  347,  213,   11,   54,   24,   13,  347,   49,
    108,  232,  320,  426,  223,  321,  221,  195,  201,  173,
     18,   49,  139,   41,  296,  426,   42,   44,  264,  222,
      7,  235,  286,  198,   49,   52,    5,  147,  288,  117,
    150,  317,  263,    4,   50,  249,  250,  239,    1,   95,
    130,  173,  189,  155,  213,   11,   54,   22,  244,  271,
    192,  108,  323,  286,  101,  223,  321,  221,  294,  226,
    204,   18,  348,  257,   41,  166,  283,   42,   44,  264,
    222,   28,  235,  300,  198,  348,   52,    5,  247,  288,
    117,  348,   94,  206,    4,   50,  249,  250,  239,    1,
     95,  129,   22,  189,  277,  213,   11,   54,   91,  274,
    224,  426,  108,  323,  216,  156,  223,  321,  221,  132,
    180,  262,   18,  426,  100,   41,   12,  288,   42,   44,
    264,  222,   15,  235,  216,  198,  254,   52,    5,  233,
    288,  210,  190,  192,  100,    4,   50,  249,  250,  239,
      1,    3,  131,   94,  189,  192,  213,   11,   54,  269,
     10,  204,  290,  108,  325,  216,  224,  223,  321,  221,
     23,  226,  211,   33,  315,  100,   45,  513,   92,   42,
     44,  264,  222,  102,  235,  178,  198,  268,   52,    5,
    275,  288,  161,  192,   37,   25,    4,   50,  249,  250,
    239,    1,  286,  129,  172,  187,  305,  213,   11,   54,
    164,  283,  310,  141,  108,  281,  281,  236,  223,  321,
    221,  169,  226,  230,   18,  122,  171,   41,  225,  175,
     42,   44,  264,  222,  144,  235,  303,  198,  134,   52,
      5,  265,  288,  151,  286,  192,  175,    4,   50,  249,
    250,  239,    1,  286,  128,   94,  189,  143,  213,   11,
     54,  219,  152,  207,  193,  108,  149,  281,   31,  223,
    321,  221,  100,  226,   21,    6,  286,  288,   41,  158,
     16,   42,   44,  264,  222,  102,  235,  238,  198,  286,
     52,    5,  157,  288,  281,  122,  168,  283,    4,   50,
    249,  250,  239,    1,   30,   93,  308,   51,  215,  213,
     11,   54,   53,  251,  140,  248,  108,  245,  304,  116,
    223,  321,  221,  111,  226,  176,   18,  270,  266,   41,
    224,  322,   42,   44,  264,  222,    7,  235,  259,  198,
    147,   52,    5,  257,  288,   43,   40,   38,   83,    4,
     50,  241,  214,  204,  319,  280,   88,  107,  138,  182,
     97,   64,  311,  312,  313,  316,   95,  281,  298,  258,
    142,  234,   94,  105,  272,  197,  231,  482,  237,  323,
     37,  133,  324,  241,  214,  204,  319,  314,   88,  107,
    296,  183,   97,   82,   84,   43,   40,   38,   95,  296,
    296,  258,  296,  296,  296,  159,  272,  197,  231,  296,
    237,  323,  311,  312,  313,  316,  241,  296,  204,  296,
    296,  103,  296,  296,  199,  104,   77,  296,  296,  110,
    296,   95,  296,  296,  258,  278,  296,  296,   34,  272,
    197,  231,  279,  237,  323,   43,   40,   38,  296,  296,
    296,  241,   26,  204,  196,  276,  103,  296,   16,  199,
    104,   77,  311,  312,  313,  316,   95,  192,  296,  258,
    146,  296,  296,  296,  272,  197,  231,  296,  237,  323,
    286,  393,   39,   35,  243,  296,  296,  296,  296,  191,
    276,  296,   26,  318,  252,  253,  126,  296,   16,  249,
    250,  239,    1,  296,  296,  131,  296,  261,  213,   11,
     54,  296,  296,  296,  426,  108,  393,  393,  393,  223,
    321,  221,  241,  296,  204,  299,  426,  103,  107,  296,
    183,   97,   82,  393,  393,  393,  393,   95,  296,  260,
    258,   52,  296,  296,  288,  272,  197,  231,  296,  237,
    323,  293,  296,  296,  296,  296,  296,  249,  250,  239,
      2,  296,  295,  296,  296,  296,  213,   11,   54,  296,
    296,  177,  296,  108,  136,  296,  296,  223,  321,  221,
    296,  296,  296,  293,   43,   40,   38,  296,  296,  249,
    250,  239,    2,  296,  295,   43,   40,   38,  213,   11,
     54,  311,  312,  313,  316,  108,  296,  291,   14,  223,
    321,  221,  311,  312,  313,  316,  296,  296,  241,  296,
    204,  296,  192,  103,  296,  296,  199,  104,   77,  296,
    296,  296,  296,   95,  383,  296,  258,  296,  296,  297,
     14,  272,  197,  231,  296,  237,  323,  383,  296,  296,
    241,  296,  204,  383,  296,   99,  296,  287,  199,  120,
     48,  241,  112,  204,  296,   95,  103,  296,  258,  199,
    120,   74,  296,  272,  197,  231,   95,  237,  323,  258,
    455,  296,  296,  455,  272,  197,  231,  455,  237,  323,
    241,  296,  204,  296,  296,  103,  200,  296,  199,  120,
     74,  296,  296,  296,  296,   95,  296,  296,  258,  278,
    296,  296,   34,  272,  197,  231,  279,  237,  323,  241,
    455,  204,  296,  296,   99,  202,  296,  199,  120,   56,
    241,  211,  204,  296,   95,  103,  296,  258,  199,  120,
     74,  296,  272,  197,  231,   95,  237,  323,  258,  227,
    296,  296,  296,  272,  197,  231,  296,  237,  323,  241,
    296,  204,  148,  296,  103,  203,   86,  199,  120,   73,
    296,  296,  286,  296,   95,  296,  296,  258,  278,  296,
    296,   34,  272,  197,  231,  279,  237,  323,  241,  296,
    204,  175,  296,  103,  296,  296,  199,  120,   75,  241,
    296,  204,  296,   95,  103,  296,  258,  199,  120,   63,
    296,  272,  197,  231,   95,  237,  323,  258,  229,  192,
    296,  296,  272,  197,  231,  296,  237,  323,  241,  296,
    204,  380,  296,  103,  296,  296,  199,  120,   58,  296,
    296,  296,  296,   95,  380,  296,  258,  296,  296,  296,
    380,  272,  197,  231,  296,  237,  323,  241,  296,  204,
    296,  296,  103,  296,  296,  199,  120,   71,  241,  296,
    204,  296,   95,  103,  296,  258,  199,  120,   79,  296,
    272,  197,  231,   95,  237,  323,  258,  296,  296,  296,
    154,  272,  197,  231,   87,  237,  323,  241,  296,  204,
    286,  296,  103,  296,  296,  199,  120,   70,  296,  296,
    296,  296,   95,  296,  296,  258,  296,  296,  296,  175,
    272,  197,  231,  296,  237,  323,  241,  296,  204,  296,
    296,  103,  296,  296,  199,  120,   56,  241,  296,  204,
    296,   95,  103,  296,  258,  199,  120,   46,  296,  272,
    197,  231,   95,  237,  323,  258,  296,  296,  296,  296,
    272,  197,  231,  296,  237,  323,  241,  296,  204,  296,
    296,  103,  296,  296,  199,  120,   78,  296,  296,  296,
    296,   95,  296,  296,  258,  296,  296,  296,  296,  272,
    197,  231,  296,  237,  323,  241,  296,  204,  296,  296,
    103,  296,  296,  199,  120,   66,  241,  296,  204,  296,
     95,  103,  296,  258,  199,  120,   59,  296,  272,  197,
    231,   95,  237,  323,  258,  296,  296,  296,  296,  272,
    197,  231,  296,  237,  323,  241,  296,  204,  296,  296,
    103,  296,  296,  186,  109,   57,  296,  296,  296,  296,
     95,  296,  296,  258,  296,  296,  296,  296,  272,  197,
    231,  296,  237,  323,  241,  296,  204,  296,  296,  103,
    296,  296,  188,  120,   67,  241,  296,  204,  296,   95,
    103,  296,  258,  199,   96,   62,  296,  272,  197,  231,
     95,  237,  323,  258,  296,  296,  296,  296,  272,  197,
    231,  296,  237,  323,  241,  296,  204,  296,  296,  103,
    296,  296,  199,  120,   80,  296,  296,  296,  296,   95,
    296,  296,  258,  296,  296,  296,  296,  272,  197,  231,
    296,  237,  323,  241,  296,  204,  296,  296,  103,  296,
    296,  199,  120,   76,  241,  296,  204,  296,   95,  103,
    296,  258,  199,  120,   81,  296,  272,  197,  231,   95,
    237,  323,  258,  296,  296,  296,  296,  272,  197,  231,
    296,  237,  323,  241,  296,  204,  296,  296,  103,  296,
    296,  199,  120,   65,  296,  296,  296,  296,   95,  296,
    296,  258,  296,  296,  296,  296,  272,  197,  231,  296,
    237,  323,  241,  296,  204,  296,  296,  103,  296,  296,
    199,   96,   68,  241,  296,  204,  296,   95,  103,  296,
    258,  199,  120,   61,  296,  272,  197,  231,   95,  237,
    323,  258,  296,  296,  296,  296,  272,  197,  231,  296,
    237,  323,  241,  296,  204,  296,  296,  103,  296,  296,
    199,   98,   69,  296,  296,  296,  296,   95,  296,  296,
    258,  296,  296,  296,  296,  272,  197,  231,  296,  237,
    323,  241,  296,  204,  296,  296,  103,  296,  296,  199,
    120,   72,  241,  296,  204,  296,   95,  103,  296,  258,
    199,  120,   47,  296,  272,  197,  231,   95,  237,  323,
    258,  296,  296,  296,  296,  272,  197,  231,  296,  237,
    323,  241,  192,  204,  296,  296,  103,  296,  296,  199,
    120,   60,  296,  296,  351,  296,   95,  296,  217,  258,
    296,  296,  296,  296,  272,  197,  231,   26,  237,  323,
    241,  296,  204,   16,  296,  103,  426,  296,  199,  125,
    296,  241,  296,  204,  296,   95,  103,  296,  426,  199,
    118,  296,  242,  272,  197,  231,   95,  237,  323,  296,
    296,  296,  296,  246,  272,  197,  231,  296,  237,  323,
    241,  296,  204,  278,  296,  103,   34,  296,  199,  121,
    279,  296,  296,  296,  296,   95,  296,  296,  296,  296,
     26,  296,  162,  272,  197,  231,   16,  237,  323,  241,
    296,  204,  296,  296,  103,  296,  296,  199,  123,  296,
    241,  296,  204,  296,   95,  103,  296,  296,  199,  114,
    296,  296,  272,  197,  231,   95,  237,  323,  296,  296,
    296,  296,  296,  272,  197,  231,  296,  237,  323,  241,
    296,  204,  296,  145,  103,  296,  296,  199,  124,  296,
    296,  296,  296,  286,   95,   39,   35,  243,  296,  296,
    296,  296,  272,  197,  231,  296,  237,  323,  241,  296,
    204,  296,  296,  103,  296,  296,  199,  115,  296,  241,
    296,  204,  296,   95,  103,  296,  296,  199,  113,  296,
    296,  272,  197,  231,   95,  237,  323,  296,  296,  296,
    296,  296,  272,  197,  231,  228,  237,  323,  241,  296,
    204,  296,  455,  103,  296,  455,  199,  119,    3,  455,
    439,  296,  296,   95,  296,  296,  296,  296,  296,  296,
    296,  272,  197,  231,  228,  237,  323,  296,  296,  296,
    296,  455,  296,  296,  455,  296,  296,  439,  455,  439,
    439,  228,  455,  296,  439,  296,  296,  137,  455,  296,
    296,  455,  296,  296,   32,  455,  439,  286,  296,   39,
     35,  243,   29,  296,   26,  296,  439,  296,  296,  439,
     16,  455,  296,  439,  306,   43,   40,   38,  296,  296,
    296,  296,  296,  439,  296,  296,  439,  296,  455,  296,
    439,   26,  311,  312,  313,  316,  296,   16,  228,  296,
    296,  296,   43,   40,   38,  455,  296,  296,  455,  296,
    296,  296,  455,  439,  296,  296,   19,  296,  296,  311,
    312,  313,  316,  455,  296,  296,  455,  296,  296,  296,
    455,  439,  296,  296,  296,   43,   40,   38,  296,  296,
    439,  296,  296,  439,  174,  455,  296,  439,  296,  240,
    309,  296,  311,  312,  313,  316,  296,  289,  439,  296,
     36,  439,  296,  455,  296,  439,  296,  296,   43,   40,
     38,  296,  296,   43,   40,   38,  296,  296,  296,  296,
    296,   43,   40,   38,  296,  311,  312,  313,  316,  296,
    311,  312,  313,  316,  296,   43,   40,   38,  311,  312,
    313,  316,  273,   43,   40,   38,  296,  296,  296,  296,
    296,  296,  311,  312,  313,  316,  296,  296,  296,  296,
    311,  312,  313,  316,  455,  296,  296,  455,   43,   40,
     38,  455,  439,  218,   43,   40,   38,  296,  296,  296,
    296,  296,  296,  296,  296,  311,  312,  313,  316,  296,
    296,  311,  312,  313,  316,  296,  296,  296,  296,  439,
    296,  296,  439,  296,  455,  296,  439,
    );
    public static $yy_lookahead = array(
      9,   10,   11,   12,   12,   14,   14,   16,   16,   18,
     19,   20,    9,   34,  102,   12,   25,   13,   70,   16,
     29,   30,   31,   35,   33,   21,   35,   23,   95,   38,
     26,   52,   41,   42,   43,   44,   32,   46,   34,   48,
     36,   50,   51,   52,   53,   53,   98,   99,   44,   58,
     59,    9,   10,   11,   12,   22,   14,   13,   16,   15,
     18,   19,   20,   85,   86,   87,   15,   25,   17,   21,
     26,   29,   30,   31,   44,   33,   32,   35,   45,   35,
     38,   51,   34,   41,   42,   43,   44,   35,   46,   77,
     48,   47,   50,   51,   14,   53,   16,   13,   47,   47,
     58,   59,    9,   10,   11,   12,   22,   14,   35,   16,
     26,   18,   19,   20,  102,  103,   32,   44,   25,   34,
     47,   36,   29,   30,   31,   52,   33,   14,   35,   45,
     50,   38,   26,   53,   41,   42,   43,   44,   32,   46,
     66,   48,   68,   50,   51,   77,   53,   15,   35,    7,
      8,   58,   59,    9,   10,   11,   12,   12,   14,   14,
     16,   16,   18,   19,   20,    9,   53,    1,   12,   25,
    102,   82,   16,   29,   30,   31,   44,   33,   33,   35,
    106,  107,   38,   51,   44,   41,   42,   43,   44,   35,
     46,   51,   48,   82,   50,   51,   22,   53,   53,   13,
     73,   47,   58,   59,    9,   10,   11,   12,   22,   14,
     83,   16,   26,   18,   19,   20,   28,   12,   32,   45,
     25,   22,   70,   35,   29,   30,   31,   65,   33,  102,
     35,   45,   73,   38,   60,   47,   41,   42,   43,   44,
     35,   46,   83,   48,   45,   50,   51,   95,   53,   71,
     95,   52,   74,   58,   59,    9,   10,   11,   12,   81,
     14,  102,   16,   73,   18,   19,   20,   34,   90,   36,
      1,   25,   94,   83,   81,   29,   30,   31,   66,   33,
     68,   35,   13,   96,   38,   98,   99,   41,   42,   43,
     44,   15,   46,  100,   48,   26,   50,   51,   14,   53,
     71,   32,   17,   74,   58,   59,    9,   10,   11,   12,
     81,   14,   34,   16,   36,   18,   19,   20,   82,  107,
     44,   35,   25,   94,   71,   95,   29,   30,   31,   44,
     33,   78,   35,   47,   81,   38,   51,   53,   41,   42,
     43,   44,   15,   46,   71,   48,   16,   50,   51,   22,
     53,   78,   79,    1,   81,   58,   59,    9,   10,   11,
     12,   15,   14,   17,   16,    1,   18,   19,   20,   66,
     34,   68,   36,   25,   16,   71,   44,   29,   30,   31,
     28,   33,   78,   35,   52,   81,   38,   62,   63,   41,
     42,   43,   44,   47,   46,    6,   48,    8,   50,   51,
     16,   53,   73,    1,    2,   40,   58,   59,    9,   10,
     11,   12,   83,   14,   77,   16,   52,   18,   19,   20,
     98,   99,   52,   95,   25,   97,   97,   92,   29,   30,
     31,   77,   33,   49,   35,  100,   14,   38,   16,  102,
     41,   42,   43,   44,   73,   46,   14,   48,   14,   50,
     51,   36,   53,   73,   83,    1,  102,   58,   59,    9,
     10,   11,   12,   83,   14,   17,   16,   50,   18,   19,
     20,   17,   71,   64,   65,   25,   73,   97,   15,   29,
     30,   31,   81,   33,   26,   35,   83,   53,   38,   73,
     32,   41,   42,   43,   44,   47,   46,   92,   48,   83,
     50,   51,   95,   53,   97,  100,   98,   99,   58,   59,
      9,   10,   11,   12,   23,   14,   52,   16,   16,   18,
     19,   20,   16,    7,   50,   16,   25,   13,   13,   16,
     29,   30,   31,   16,   33,   16,   35,   33,   33,   38,
     44,   16,   41,   42,   43,   44,   35,   46,   16,   48,
     95,   50,   51,   96,   53,   37,   38,   39,   81,   58,
     59,   66,   67,   68,   69,   83,   71,   72,   95,   74,
     75,   76,   54,   55,   56,   57,   81,   97,   60,   84,
     95,   13,   17,   80,   89,   90,   91,    1,   93,   94,
      2,   81,  103,   66,   67,   68,   69,   99,   71,   72,
    108,   74,   75,   76,   81,   37,   38,   39,   81,  108,
    108,   84,  108,  108,  108,   95,   89,   90,   91,  108,
     93,   94,   54,   55,   56,   57,   66,  108,   68,  108,
    108,   71,  108,  108,   74,   75,   76,  108,  108,   21,
    108,   81,  108,  108,   84,    9,  108,  108,   12,   89,
     90,   91,   16,   93,   94,   37,   38,   39,  108,  108,
    108,   66,   26,   68,  104,  105,   71,  108,   32,   74,
     75,   76,   54,   55,   56,   57,   81,    1,  108,   84,
     73,  108,  108,  108,   89,   90,   91,  108,   93,   94,
     83,    2,   85,   86,   87,  108,  108,  108,  108,  104,
    105,  108,   26,    3,    4,    5,    6,  108,   32,    9,
     10,   11,   12,  108,  108,   14,  108,   16,   18,   19,
     20,  108,  108,  108,   35,   25,   37,   38,   39,   29,
     30,   31,   66,  108,   68,   69,   47,   71,   72,  108,
     74,   75,   76,   54,   55,   56,   57,   81,  108,   48,
     84,   50,  108,  108,   53,   89,   90,   91,  108,   93,
     94,    3,  108,  108,  108,  108,  108,    9,   10,   11,
     12,  108,   14,  108,  108,  108,   18,   19,   20,  108,
    108,   13,  108,   25,   27,  108,  108,   29,   30,   31,
    108,  108,  108,    3,   37,   38,   39,  108,  108,    9,
     10,   11,   12,  108,   14,   37,   38,   39,   18,   19,
     20,   54,   55,   56,   57,   25,  108,   59,   60,   29,
     30,   31,   54,   55,   56,   57,  108,  108,   66,  108,
     68,  108,    1,   71,  108,  108,   74,   75,   76,  108,
    108,  108,  108,   81,   13,  108,   84,  108,  108,   59,
     60,   89,   90,   91,  108,   93,   94,   26,  108,  108,
     66,  108,   68,   32,  108,   71,  108,  105,   74,   75,
     76,   66,   78,   68,  108,   81,   71,  108,   84,   74,
     75,   76,  108,   89,   90,   91,   81,   93,   94,   84,
      9,  108,  108,   12,   89,   90,   91,   16,   93,   94,
     66,  108,   68,  108,  108,   71,  101,  108,   74,   75,
     76,  108,  108,  108,  108,   81,  108,  108,   84,    9,
    108,  108,   12,   89,   90,   91,   16,   93,   94,   66,
     49,   68,  108,  108,   71,  101,  108,   74,   75,   76,
     66,   78,   68,  108,   81,   71,  108,   84,   74,   75,
     76,  108,   89,   90,   91,   81,   93,   94,   84,   49,
    108,  108,  108,   89,   90,   91,  108,   93,   94,   66,
    108,   68,   73,  108,   71,  101,   77,   74,   75,   76,
    108,  108,   83,  108,   81,  108,  108,   84,    9,  108,
    108,   12,   89,   90,   91,   16,   93,   94,   66,  108,
     68,  102,  108,   71,  108,  108,   74,   75,   76,   66,
    108,   68,  108,   81,   71,  108,   84,   74,   75,   76,
    108,   89,   90,   91,   81,   93,   94,   84,   49,    1,
    108,  108,   89,   90,   91,  108,   93,   94,   66,  108,
     68,   13,  108,   71,  108,  108,   74,   75,   76,  108,
    108,  108,  108,   81,   26,  108,   84,  108,  108,  108,
     32,   89,   90,   91,  108,   93,   94,   66,  108,   68,
    108,  108,   71,  108,  108,   74,   75,   76,   66,  108,
     68,  108,   81,   71,  108,   84,   74,   75,   76,  108,
     89,   90,   91,   81,   93,   94,   84,  108,  108,  108,
     73,   89,   90,   91,   77,   93,   94,   66,  108,   68,
     83,  108,   71,  108,  108,   74,   75,   76,  108,  108,
    108,  108,   81,  108,  108,   84,  108,  108,  108,  102,
     89,   90,   91,  108,   93,   94,   66,  108,   68,  108,
    108,   71,  108,  108,   74,   75,   76,   66,  108,   68,
    108,   81,   71,  108,   84,   74,   75,   76,  108,   89,
     90,   91,   81,   93,   94,   84,  108,  108,  108,  108,
     89,   90,   91,  108,   93,   94,   66,  108,   68,  108,
    108,   71,  108,  108,   74,   75,   76,  108,  108,  108,
    108,   81,  108,  108,   84,  108,  108,  108,  108,   89,
     90,   91,  108,   93,   94,   66,  108,   68,  108,  108,
     71,  108,  108,   74,   75,   76,   66,  108,   68,  108,
     81,   71,  108,   84,   74,   75,   76,  108,   89,   90,
     91,   81,   93,   94,   84,  108,  108,  108,  108,   89,
     90,   91,  108,   93,   94,   66,  108,   68,  108,  108,
     71,  108,  108,   74,   75,   76,  108,  108,  108,  108,
     81,  108,  108,   84,  108,  108,  108,  108,   89,   90,
     91,  108,   93,   94,   66,  108,   68,  108,  108,   71,
    108,  108,   74,   75,   76,   66,  108,   68,  108,   81,
     71,  108,   84,   74,   75,   76,  108,   89,   90,   91,
     81,   93,   94,   84,  108,  108,  108,  108,   89,   90,
     91,  108,   93,   94,   66,  108,   68,  108,  108,   71,
    108,  108,   74,   75,   76,  108,  108,  108,  108,   81,
    108,  108,   84,  108,  108,  108,  108,   89,   90,   91,
    108,   93,   94,   66,  108,   68,  108,  108,   71,  108,
    108,   74,   75,   76,   66,  108,   68,  108,   81,   71,
    108,   84,   74,   75,   76,  108,   89,   90,   91,   81,
     93,   94,   84,  108,  108,  108,  108,   89,   90,   91,
    108,   93,   94,   66,  108,   68,  108,  108,   71,  108,
    108,   74,   75,   76,  108,  108,  108,  108,   81,  108,
    108,   84,  108,  108,  108,  108,   89,   90,   91,  108,
     93,   94,   66,  108,   68,  108,  108,   71,  108,  108,
     74,   75,   76,   66,  108,   68,  108,   81,   71,  108,
     84,   74,   75,   76,  108,   89,   90,   91,   81,   93,
     94,   84,  108,  108,  108,  108,   89,   90,   91,  108,
     93,   94,   66,  108,   68,  108,  108,   71,  108,  108,
     74,   75,   76,  108,  108,  108,  108,   81,  108,  108,
     84,  108,  108,  108,  108,   89,   90,   91,  108,   93,
     94,   66,  108,   68,  108,  108,   71,  108,  108,   74,
     75,   76,   66,  108,   68,  108,   81,   71,  108,   84,
     74,   75,   76,  108,   89,   90,   91,   81,   93,   94,
     84,  108,  108,  108,  108,   89,   90,   91,  108,   93,
     94,   66,    1,   68,  108,  108,   71,  108,  108,   74,
     75,   76,  108,  108,   13,  108,   81,  108,   17,   84,
    108,  108,  108,  108,   89,   90,   91,   26,   93,   94,
     66,  108,   68,   32,  108,   71,   35,  108,   74,   75,
    108,   66,  108,   68,  108,   81,   71,  108,   47,   74,
     75,  108,   88,   89,   90,   91,   81,   93,   94,  108,
    108,  108,  108,   88,   89,   90,   91,  108,   93,   94,
     66,  108,   68,    9,  108,   71,   12,  108,   74,   75,
     16,  108,  108,  108,  108,   81,  108,  108,  108,  108,
     26,  108,   28,   89,   90,   91,   32,   93,   94,   66,
    108,   68,  108,  108,   71,  108,  108,   74,   75,  108,
     66,  108,   68,  108,   81,   71,  108,  108,   74,   75,
    108,  108,   89,   90,   91,   81,   93,   94,  108,  108,
    108,  108,  108,   89,   90,   91,  108,   93,   94,   66,
    108,   68,  108,   73,   71,  108,  108,   74,   75,  108,
    108,  108,  108,   83,   81,   85,   86,   87,  108,  108,
    108,  108,   89,   90,   91,  108,   93,   94,   66,  108,
     68,  108,  108,   71,  108,  108,   74,   75,  108,   66,
    108,   68,  108,   81,   71,  108,  108,   74,   75,  108,
    108,   89,   90,   91,   81,   93,   94,  108,  108,  108,
    108,  108,   89,   90,   91,    2,   93,   94,   66,  108,
     68,  108,    9,   71,  108,   12,   74,   75,   15,   16,
     17,  108,  108,   81,  108,  108,  108,  108,  108,  108,
    108,   89,   90,   91,    2,   93,   94,  108,  108,  108,
    108,    9,  108,  108,   12,  108,  108,   44,   16,   17,
     47,    2,   49,  108,   51,  108,  108,   73,    9,  108,
    108,   12,  108,  108,   15,   16,   17,   83,  108,   85,
     86,   87,   24,  108,   26,  108,   44,  108,  108,   47,
     32,   49,  108,   51,   52,   37,   38,   39,  108,  108,
    108,  108,  108,   44,  108,  108,   47,  108,   49,  108,
     51,   26,   54,   55,   56,   57,  108,   32,    2,  108,
    108,  108,   37,   38,   39,    9,  108,  108,   12,  108,
    108,  108,   16,   17,  108,  108,    2,  108,  108,   54,
     55,   56,   57,    9,  108,  108,   12,  108,  108,  108,
     16,   17,  108,  108,  108,   37,   38,   39,  108,  108,
     44,  108,  108,   47,   13,   49,  108,   51,  108,   13,
     52,  108,   54,   55,   56,   57,  108,   13,   44,  108,
      2,   47,  108,   49,  108,   51,  108,  108,   37,   38,
     39,  108,  108,   37,   38,   39,  108,  108,  108,  108,
    108,   37,   38,   39,  108,   54,   55,   56,   57,  108,
     54,   55,   56,   57,  108,   37,   38,   39,   54,   55,
     56,   57,   36,   37,   38,   39,  108,  108,  108,  108,
    108,  108,   54,   55,   56,   57,  108,  108,  108,  108,
     54,   55,   56,   57,    9,  108,  108,   12,   37,   38,
     39,   16,   17,   36,   37,   38,   39,  108,  108,  108,
    108,  108,  108,  108,  108,   54,   55,   56,   57,  108,
    108,   54,   55,   56,   57,  108,  108,  108,  108,   44,
    108,  108,   47,  108,   49,  108,   51,
);
    const YY_SHIFT_USE_DFLT = -22;
    const YY_SHIFT_MAX = 230;
    public static $yy_shift_ofst = array(
    -22,  501,  501,   93,  399,  399,  450,   93,   93,   93,
    399,  450,   -9,   93,   93,   93,   93,   93,   93,  144,
     93,  195,   93,   93,   93,  246,  195,   93,   93,   93,
     93,   93,  297,   93,   93,   93,   93,  348,   42,   42,
     42,   42,   42,   42,   42,   42, 1768, 1795, 1795,  701,
    758, 1521,   80,  676,  113,  790, 1927, 1828, 1896,  568,
    768, 1861,  757, 1866, 1874, 1888,  618,  518, 1921, 1921,
   1921, 1921, 1921, 1921, 1921, 1921, 1921, 1921, 1921, 1921,
   1921, 1921, 1921, 1584,  636,  285,  676,  676,  346,  113,
    113,  402,  700, 1723,   -8,  910,  831,  269, 1028,   51,
      3,    3,  422,  448,  352,  106,  422,  106,  458,  364,
    434,  454,  106,  166,  166,  166,  166,  565,  166,  166,
    166,  586,  565,  166,  166,  -22,  -22, 1752, 1769, 1826,
   1844, 1945,  145,  979,  156,  132,  284,  106,  140,  106,
     30,  140,  140,   30,  106,  106,  106,  140,  106,  106,
    140,  106,  327,  106,  106,  106,  140,  140,  106,  140,
    205,  106,  284,  166,  565,  588,  565,  588,  565,  166,
    166,  -12,  166,  -22,  -22,  -22,  -22,  -22,  -22,  689,
      4,   44,   84,  186,   73,  881,  199,  188,  174,  286,
     48,  336,  384,  389,  332,  142,  -21,   52,  154,   33,
     85,  276,  278,  233,  515,  509,  474,  516,  502,  464,
    491,  415,  417,  432,  514,  370,  463,  506,  365,  513,
    -12,  517,  504,  519,  505,  511,  496,  525,  532,  330,
    358,
);
    const YY_REDUCE_USE_DFLT = -89;
    const YY_REDUCE_MAX = 178;
    public static $yy_reduce_ofst = array(
    325,  527,  495,  666,  595,  560,  863,  874,  834,  805,
    762,  794, 1179, 1455, 1208, 1012, 1386, 1139, 1070, 1110,
   1150, 1219, 1248, 1277, 1288, 1317, 1346, 1357, 1415, 1426,
   1081, 1041, 1001,  972,  943,  932,  903, 1484, 1495, 1622,
   1633, 1662, 1593, 1564, 1553, 1524, 1704,  607, 1590,  178,
     74, 1027,  229,  899,  273,  212,  -22,  -22,  -22,  -22,
    -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,
    -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,
    -22,  -22,  -22,  380,  329,  187,  159,  127,  -52,  253,
    304,   12,  303,  152,  193,  328,   68,   68,   68,  322,
    328,  407,  405,  322,   68,  190,  335,  416,  403,   68,
    401,  354,  371,   68,   68,   68,  337,  322,   68,   68,
     68,   68,  408,   68,   68,   68,  409,  455,  455,  455,
    455,  455,  510,  480,  455,  455,  477,  482,  457,  482,
    473,  457,  457,  485,  482,  482,  482,  457,  482,  482,
    457,  482,  503,  482,  482,  482,  457,  457,  482,  457,
    520,  482,  523,  -88,  498,  489,  498,  489,  498,  -88,
    -88,  -67,  -88,  111,  155,   89,  236,  230,  162,
);
    public static $yyExpectedTokens = array(
         array(),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 52, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(9, 10, 11, 12, 14, 16, 18, 19, 20, 25, 29, 30, 31, 33, 35, 38, 41, 42, 43, 44, 46, 48, 50, 51, 53, 58, 59, ),
         array(24, 26, 32, 37, 38, 39, 54, 55, 56, 57, ),
         array(26, 32, 37, 38, 39, 54, 55, 56, 57, ),
         array(26, 32, 37, 38, 39, 54, 55, 56, 57, ),
         array(14, 16, 48, 50, 53, ),
         array(3, 9, 10, 11, 12, 14, 18, 19, 20, 25, 29, 30, 31, 59, 60, ),
         array(1, 13, 17, 26, 32, 35, 47, ),
         array(14, 16, 50, 53, ),
         array(1, 26, 32, ),
         array(14, 35, 53, ),
         array(3, 9, 10, 11, 12, 14, 18, 19, 20, 25, 29, 30, 31, 59, 60, ),
         array(36, 37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 52, 54, 55, 56, 57, ),
         array(36, 37, 38, 39, 54, 55, 56, 57, ),
         array(13, 37, 38, 39, 54, 55, 56, 57, ),
         array(13, 37, 38, 39, 54, 55, 56, 57, ),
         array(13, 37, 38, 39, 54, 55, 56, 57, ),
         array(27, 37, 38, 39, 54, 55, 56, 57, ),
         array(13, 37, 38, 39, 54, 55, 56, 57, ),
         array(13, 37, 38, 39, 54, 55, 56, 57, ),
         array(2, 37, 38, 39, 54, 55, 56, 57, ),
         array(21, 37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, 60, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(37, 38, 39, 54, 55, 56, 57, ),
         array(9, 12, 16, 26, 28, 32, ),
         array(9, 12, 16, 26, 32, ),
         array(17, 44, 51, ),
         array(1, 26, 32, ),
         array(1, 26, 32, ),
         array(15, 17, 47, ),
         array(14, 35, 53, ),
         array(14, 35, 53, ),
         array(1, 2, ),
         array(3, 4, 5, 6, 9, 10, 11, 12, 18, 19, 20, 25, 29, 30, 31, ),
         array(2, 9, 12, 15, 16, 17, 44, 47, 49, 51, ),
         array(12, 14, 16, 53, ),
         array(9, 12, 16, 49, ),
         array(1, 13, 26, 32, ),
         array(1, 13, 26, 32, ),
         array(1, 13, 26, 32, ),
         array(15, 17, 47, ),
         array(9, 12, 16, ),
         array(9, 12, 16, ),
         array(14, 16, ),
         array(17, 47, ),
         array(1, 28, ),
         array(26, 32, ),
         array(14, 16, ),
         array(26, 32, ),
         array(26, 32, ),
         array(1, 52, ),
         array(14, 53, ),
         array(1, 17, ),
         array(26, 32, ),
         array(1, ),
         array(1, ),
         array(1, ),
         array(1, ),
         array(17, ),
         array(1, ),
         array(1, ),
         array(1, ),
         array(1, ),
         array(17, ),
         array(1, ),
         array(1, ),
         array(),
         array(),
         array(2, 9, 12, 16, 17, 44, 47, 49, 51, 52, ),
         array(2, 9, 12, 15, 16, 17, 44, 47, 49, 51, ),
         array(2, 9, 12, 16, 17, 44, 47, 49, 51, ),
         array(2, 9, 12, 16, 17, 44, 47, 49, 51, ),
         array(9, 12, 16, 17, 44, 47, 49, 51, ),
         array(12, 14, 16, 33, 53, ),
         array(9, 12, 16, 49, ),
         array(9, 12, 16, ),
         array(15, 44, 51, ),
         array(14, 53, ),
         array(26, 32, ),
         array(44, 51, ),
         array(26, 32, ),
         array(44, 51, ),
         array(44, 51, ),
         array(44, 51, ),
         array(44, 51, ),
         array(26, 32, ),
         array(26, 32, ),
         array(26, 32, ),
         array(44, 51, ),
         array(26, 32, ),
         array(26, 32, ),
         array(44, 51, ),
         array(26, 32, ),
         array(15, 22, ),
         array(26, 32, ),
         array(26, 32, ),
         array(26, 32, ),
         array(44, 51, ),
         array(44, 51, ),
         array(26, 32, ),
         array(44, 51, ),
         array(12, 35, ),
         array(26, 32, ),
         array(14, 53, ),
         array(1, ),
         array(17, ),
         array(2, ),
         array(17, ),
         array(2, ),
         array(17, ),
         array(1, ),
         array(1, ),
         array(35, ),
         array(1, ),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(2, 35, 37, 38, 39, 47, 54, 55, 56, 57, ),
         array(13, 21, 23, 26, 32, 34, 36, 44, ),
         array(13, 15, 26, 32, 35, 47, ),
         array(13, 22, 26, 32, 45, ),
         array(13, 22, 26, 32, 45, ),
         array(35, 44, 47, 52, ),
         array(9, 12, 16, 49, ),
         array(22, 45, 52, ),
         array(28, 35, 47, ),
         array(22, 45, 60, ),
         array(35, 47, ),
         array(21, 34, ),
         array(34, 36, ),
         array(16, 49, ),
         array(6, 8, ),
         array(44, 52, ),
         array(7, 8, ),
         array(34, 52, ),
         array(35, 47, ),
         array(35, 47, ),
         array(22, 45, ),
         array(34, 36, ),
         array(15, 44, ),
         array(34, 36, ),
         array(34, 36, ),
         array(13, ),
         array(16, ),
         array(50, ),
         array(7, ),
         array(16, ),
         array(52, ),
         array(23, ),
         array(36, ),
         array(50, ),
         array(14, ),
         array(13, ),
         array(52, ),
         array(15, ),
         array(16, ),
         array(40, ),
         array(16, ),
         array(35, ),
         array(16, ),
         array(33, ),
         array(16, ),
         array(33, ),
         array(35, ),
         array(44, ),
         array(16, ),
         array(16, ),
         array(16, ),
         array(16, ),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
         array(),
);
    public static $yy_default = array(
    336,  512,  512,  512,  497,  497,  512,  474,  474,  474,
    512,  512,  512,  512,  512,  512,  512,  512,  512,  512,
    512,  512,  512,  512,  512,  512,  512,  512,  512,  512,
    512,  512,  512,  512,  512,  512,  512,  512,  512,  512,
    512,  512,  512,  512,  512,  512,  377,  377,  356,  512,
    512,  413,  512,  377,  512,  512,  512,  512,  512,  512,
    512,  512,  382,  512,  349,  512,  512,  512,  382,  379,
    389,  388,  384,  402,  473,  397,  498,  500,  401,  361,
    472,  499,  349,  377,  377,  487,  377,  377,  429,  512,
    512,  368,  326,  428,  512,  439,  391,  391,  391,  429,
    439,  439,  512,  429,  391,  377,  512,  377,  377,  391,
    512,  371,  358,  395,  394,  396,  373,  429,  400,  404,
    391,  404,  484,  406,  405,  481,  334,  428,  428,  428,
    428,  428,  512,  441,  439,  455,  512,  363,  435,  354,
    434,  437,  433,  432,  359,  357,  364,  436,  353,  367,
    466,  365,  512,  352,  350,  360,  467,  465,  346,  464,
    439,  366,  512,  369,  461,  475,  488,  476,  485,  372,
    422,  439,  374,  480,  439,  480,  480,  439,  334,  413,
    409,  413,  403,  403,  413,  440,  403,  413,  403,  413,
    512,  512,  512,  332,  409,  512,  512,  512,  423,  403,
    512,  409,  512,  512,  512,  512,  512,  512,  512,  418,
    385,  512,  512,  512,  512,  512,  512,  512,  415,  512,
    455,  512,  512,  512,  411,  486,  409,  512,  512,  512,
    512,  419,  407,  362,  445,  418,  425,  424,  420,  339,
    460,  421,  483,  398,  416,  340,  399,  455,  378,  337,
    338,  330,  328,  329,  442,  443,  444,  438,  392,  393,
    427,  426,  386,  417,  408,  390,  410,  331,  333,  335,
    412,  470,  414,  415,  503,  478,  495,  471,  459,  458,
    375,  457,  344,  462,  508,  493,  376,  496,  456,  509,
    494,  501,  504,  511,  510,  507,  505,  502,  506,  345,
    468,  469,  446,  355,  341,  452,  450,  454,  448,  453,
    447,  489,  490,  491,  463,  449,  492,  451,  327,  342,
    343,  370,  430,  431,  479,  477,
);
    const YYNOCODE = 109;
    const YYSTACKDEPTH = 500;
    const YYNSTATE = 326;
    const YYNRULE = 186;
    const YYERRORSYMBOL = 61;
    const YYERRSYMDT = 'yy0';
    const YYFALLBACK = 0;
    public static $yyFallback = array(
    );
    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public $yyTraceFILE;
    public $yyTracePrompt;
    public $yyidx;                    /* Index of top element in stack */
    public $yyerrcnt;                 /* Shifts left before out of the error */
    public $yystack = array();  /* The parser's stack */

    public $yyTokenName = array(
  '$',             'VERT',          'COLON',         'TEXT',        
  'STRIPON',       'STRIPOFF',      'LITERALSTART',  'LITERALEND',  
  'LITERAL',       'SIMPELOUTPUT',  'SIMPLETAG',     'SMARTYBLOCKCHILDPARENT',
  'LDEL',          'RDEL',          'DOLLARID',      'EQUAL',       
  'ID',            'PTR',           'LDELMAKENOCACHE',  'LDELIF',      
  'LDELFOR',       'SEMICOLON',     'INCDEC',        'TO',          
  'STEP',          'LDELFOREACH',   'SPACE',         'AS',          
  'APTR',          'LDELSETFILTER',  'CLOSETAG',      'LDELSLASH',   
  'ATTR',          'INTEGER',       'COMMA',         'OPENP',       
  'CLOSEP',        'MATH',          'UNIMATH',       'ISIN',        
  'QMARK',         'NOT',           'TYPECAST',      'HEX',         
  'DOT',           'INSTANCEOF',    'SINGLEQUOTESTRING',  'DOUBLECOLON', 
  'NAMESPACE',     'AT',            'HATCH',         'OPENB',       
  'CLOSEB',        'DOLLAR',        'LOGOP',         'SLOGOP',      
  'TLOGOP',        'SINGLECOND',    'ARRAYOPEN',     'QUOTE',       
  'BACKTICK',      'error',         'start',         'template',    
  'literal_e2',    'literal_e1',    'smartytag',     'tagbody',     
  'tag',           'outattr',       'eqoutattr',     'varindexed',  
  'output',        'attributes',    'variable',      'value',       
  'expr',          'modifierlist',  'statement',     'statements',  
  'foraction',     'varvar',        'modparameters',  'attribute',   
  'ternary',       'tlop',          'lop',           'scond',       
  'array',         'function',      'ns1',           'doublequoted_with_quotes',
  'static_class_access',  'arraydef',      'object',        'arrayindex',  
  'indexdef',      'varvarele',     'objectchain',   'objectelement',
  'method',        'params',        'modifier',      'modparameter',
  'arrayelements',  'arrayelement',  'doublequoted',  'doublequotedcontent',
    );

    public static $yyRuleName = array(
  'start ::= template',
  'template ::= template TEXT',
  'template ::= template STRIPON',
  'template ::= template STRIPOFF',
  'template ::= template LITERALSTART literal_e2 LITERALEND',
  'literal_e2 ::= literal_e1 LITERALSTART literal_e1 LITERALEND',
  'literal_e2 ::= literal_e1',
  'literal_e1 ::= literal_e1 LITERAL',
  'literal_e1 ::=',
  'template ::= template smartytag',
  'template ::=',
  'smartytag ::= SIMPELOUTPUT',
  'smartytag ::= SIMPLETAG',
  'smartytag ::= SMARTYBLOCKCHILDPARENT',
  'smartytag ::= LDEL tagbody RDEL',
  'smartytag ::= tag RDEL',
  'tagbody ::= outattr',
  'tagbody ::= DOLLARID eqoutattr',
  'tagbody ::= varindexed eqoutattr',
  'eqoutattr ::= EQUAL outattr',
  'outattr ::= output attributes',
  'output ::= variable',
  'output ::= value',
  'output ::= expr',
  'tag ::= LDEL ID attributes',
  'tag ::= LDEL ID',
  'tag ::= LDEL ID modifierlist attributes',
  'tag ::= LDEL ID PTR ID attributes',
  'tag ::= LDEL ID PTR ID modifierlist attributes',
  'tag ::= LDELMAKENOCACHE DOLLARID',
  'tag ::= LDELIF expr',
  'tag ::= LDELIF expr attributes',
  'tag ::= LDELIF statement',
  'tag ::= LDELIF statement attributes',
  'tag ::= LDELFOR statements SEMICOLON expr SEMICOLON varindexed foraction attributes',
  'foraction ::= EQUAL expr',
  'foraction ::= INCDEC',
  'tag ::= LDELFOR statement TO expr attributes',
  'tag ::= LDELFOR statement TO expr STEP expr attributes',
  'tag ::= LDELFOREACH SPACE expr AS varvar attributes',
  'tag ::= LDELFOREACH SPACE expr AS varvar APTR varvar attributes',
  'tag ::= LDELFOREACH attributes',
  'tag ::= LDELSETFILTER ID modparameters',
  'tag ::= LDELSETFILTER ID modparameters modifierlist',
  'smartytag ::= CLOSETAG',
  'tag ::= LDELSLASH ID',
  'tag ::= LDELSLASH ID modifierlist',
  'tag ::= LDELSLASH ID PTR ID',
  'tag ::= LDELSLASH ID PTR ID modifierlist',
  'attributes ::= attributes attribute',
  'attributes ::= attribute',
  'attributes ::=',
  'attribute ::= SPACE ID EQUAL ID',
  'attribute ::= ATTR expr',
  'attribute ::= ATTR value',
  'attribute ::= SPACE ID',
  'attribute ::= SPACE expr',
  'attribute ::= SPACE value',
  'attribute ::= SPACE INTEGER EQUAL expr',
  'statements ::= statement',
  'statements ::= statements COMMA statement',
  'statement ::= DOLLARID EQUAL INTEGER',
  'statement ::= DOLLARID EQUAL expr',
  'statement ::= varindexed EQUAL expr',
  'statement ::= OPENP statement CLOSEP',
  'expr ::= value',
  'expr ::= ternary',
  'expr ::= DOLLARID COLON ID',
  'expr ::= expr MATH value',
  'expr ::= expr UNIMATH value',
  'expr ::= expr tlop value',
  'expr ::= expr lop expr',
  'expr ::= expr scond',
  'expr ::= expr ISIN array',
  'expr ::= expr ISIN value',
  'ternary ::= OPENP expr CLOSEP QMARK DOLLARID COLON expr',
  'ternary ::= OPENP expr CLOSEP QMARK expr COLON expr',
  'value ::= variable',
  'value ::= UNIMATH value',
  'value ::= NOT value',
  'value ::= TYPECAST value',
  'value ::= variable INCDEC',
  'value ::= HEX',
  'value ::= INTEGER',
  'value ::= INTEGER DOT INTEGER',
  'value ::= INTEGER DOT',
  'value ::= DOT INTEGER',
  'value ::= ID',
  'value ::= function',
  'value ::= OPENP expr CLOSEP',
  'value ::= variable INSTANCEOF ns1',
  'value ::= variable INSTANCEOF variable',
  'value ::= SINGLEQUOTESTRING',
  'value ::= doublequoted_with_quotes',
  'value ::= varindexed DOUBLECOLON static_class_access',
  'value ::= smartytag',
  'value ::= value modifierlist',
  'value ::= NAMESPACE',
  'value ::= arraydef',
  'value ::= ns1 DOUBLECOLON static_class_access',
  'ns1 ::= ID',
  'ns1 ::= NAMESPACE',
  'variable ::= DOLLARID',
  'variable ::= varindexed',
  'variable ::= varvar AT ID',
  'variable ::= object',
  'variable ::= HATCH ID HATCH',
  'variable ::= HATCH ID HATCH arrayindex',
  'variable ::= HATCH variable HATCH',
  'variable ::= HATCH variable HATCH arrayindex',
  'varindexed ::= DOLLARID arrayindex',
  'varindexed ::= varvar arrayindex',
  'arrayindex ::= arrayindex indexdef',
  'arrayindex ::=',
  'indexdef ::= DOT DOLLARID',
  'indexdef ::= DOT varvar',
  'indexdef ::= DOT varvar AT ID',
  'indexdef ::= DOT ID',
  'indexdef ::= DOT INTEGER',
  'indexdef ::= DOT LDEL expr RDEL',
  'indexdef ::= OPENB ID CLOSEB',
  'indexdef ::= OPENB ID DOT ID CLOSEB',
  'indexdef ::= OPENB SINGLEQUOTESTRING CLOSEB',
  'indexdef ::= OPENB INTEGER CLOSEB',
  'indexdef ::= OPENB DOLLARID CLOSEB',
  'indexdef ::= OPENB variable CLOSEB',
  'indexdef ::= OPENB value CLOSEB',
  'indexdef ::= OPENB expr CLOSEB',
  'indexdef ::= OPENB CLOSEB',
  'varvar ::= DOLLARID',
  'varvar ::= DOLLAR',
  'varvar ::= varvar varvarele',
  'varvarele ::= ID',
  'varvarele ::= SIMPELOUTPUT',
  'varvarele ::= LDEL expr RDEL',
  'object ::= varindexed objectchain',
  'objectchain ::= objectelement',
  'objectchain ::= objectchain objectelement',
  'objectelement ::= PTR ID arrayindex',
  'objectelement ::= PTR varvar arrayindex',
  'objectelement ::= PTR LDEL expr RDEL arrayindex',
  'objectelement ::= PTR ID LDEL expr RDEL arrayindex',
  'objectelement ::= PTR method',
  'function ::= ns1 OPENP params CLOSEP',
  'method ::= ID OPENP params CLOSEP',
  'method ::= DOLLARID OPENP params CLOSEP',
  'params ::= params COMMA expr',
  'params ::= expr',
  'params ::=',
  'modifierlist ::= modifierlist modifier modparameters',
  'modifierlist ::= modifier modparameters',
  'modifier ::= VERT AT ID',
  'modifier ::= VERT ID',
  'modparameters ::= modparameters modparameter',
  'modparameters ::=',
  'modparameter ::= COLON value',
  'modparameter ::= COLON UNIMATH value',
  'modparameter ::= COLON array',
  'static_class_access ::= method',
  'static_class_access ::= method objectchain',
  'static_class_access ::= ID',
  'static_class_access ::= DOLLARID arrayindex',
  'static_class_access ::= DOLLARID arrayindex objectchain',
  'lop ::= LOGOP',
  'lop ::= SLOGOP',
  'tlop ::= TLOGOP',
  'scond ::= SINGLECOND',
  'arraydef ::= OPENB arrayelements CLOSEB',
  'arraydef ::= ARRAYOPEN arrayelements CLOSEP',
  'arrayelements ::= arrayelement',
  'arrayelements ::= arrayelements COMMA arrayelement',
  'arrayelements ::=',
  'arrayelement ::= value APTR expr',
  'arrayelement ::= ID APTR expr',
  'arrayelement ::= expr',
  'doublequoted_with_quotes ::= QUOTE QUOTE',
  'doublequoted_with_quotes ::= QUOTE doublequoted QUOTE',
  'doublequoted ::= doublequoted doublequotedcontent',
  'doublequoted ::= doublequotedcontent',
  'doublequotedcontent ::= BACKTICK variable BACKTICK',
  'doublequotedcontent ::= BACKTICK expr BACKTICK',
  'doublequotedcontent ::= DOLLARID',
  'doublequotedcontent ::= LDEL variable RDEL',
  'doublequotedcontent ::= LDEL expr RDEL',
  'doublequotedcontent ::= smartytag',
  'doublequotedcontent ::= TEXT',
    );

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[$tokenType];
        } else {
            return 'Unknown';
        }
    }

    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:  break;   /* If no destructor action specified: do nothing */
        }
    }

    public function yy_pop_parser_stack()
    {
        if (empty($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite($this->yyTraceFILE,
                $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[$yytos->major] .
                    "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx--;

        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== Array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        static $res3 = array();
        static $res4 = array();
        $state = $this->yystack[$this->yyidx]->stateno;
        $expected = self::$yyExpectedTokens[$state];
        if (isset($res3[$state][$token])) {
            if ($res3[$state][$token]) {
                return $expected;
            }
        } else {
            if ($res3[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return $expected;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno][0]);
                    if (isset(self::$yyExpectedTokens[$nextstate])) {
                $expected = array_merge($expected, self::$yyExpectedTokens[$nextstate]);
                        if (isset($res4[$nextstate][$token])) {
                            if ($res4[$nextstate][$token]) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        } else {
                            if ($res4[$nextstate][$token] = in_array($token, self::$yyExpectedTokens[$nextstate], true)) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
    $this->yyidx = $yyidx;
    $this->yystack = $stack;

        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        static $res = array();
        static $res2 = array();
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[$this->yyidx]->stateno;
        if (isset($res[$state][$token])) {
            if ($res[$state][$token]) {
                return true;
            }
        } else {
            if ($res[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return true;
            }
       }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno][0]);
                    if (isset($res2[$nextstate][$token])) {
                        if ($res2[$nextstate][$token]) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    } else {
                        if ($res2[$nextstate][$token] = (isset(self::$yyExpectedTokens[$nextstate]) && in_array($token, self::$yyExpectedTokens[$nextstate], true))) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return true;
    }

   public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[$this->yyidx]->stateno;

        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[$stateno])) {
            // no shift actions
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_shift_ofst[$stateno];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
              self::$yy_lookahead[$i] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback)
                   && ($iFallback = self::$yyFallback[$iLookAhead]) != 0) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . 'FALLBACK ' .
                        $this->yyTokenName[$iLookAhead] . ' => ' .
                        $this->yyTokenName[$iFallback] . "\n");
                }

                return $this->yy_find_shift_action($iFallback);
            }

            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */

        if (!isset(self::$yy_reduce_ofst[$stateno])) {
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_reduce_ofst[$stateno];
        if ($i === self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
              self::$yy_lookahead[$i] != $iLookAhead) {
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx--;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
// line 220 "../smarty/lexer/smarty_internal_templateparser.y"

    $this->internalError = true;
    $this->compiler->trigger_template_error('Stack overflow in template parser');

            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        $this->yystack[] = $yytos;
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf($this->yyTraceFILE, "%sShift %d\n", $this->yyTracePrompt,
                $yyNewState);
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i++) {
                fprintf($this->yyTraceFILE, " %s",
                    $this->yyTokenName[$this->yystack[$i]->major]);
            }
            fwrite($this->yyTraceFILE,"\n");
        }
    }

    public static $yyRuleInfo = array(
  array( 0 => 62, 1 => 1 ),
  array( 0 => 63, 1 => 2 ),
  array( 0 => 63, 1 => 2 ),
  array( 0 => 63, 1 => 2 ),
  array( 0 => 63, 1 => 4 ),
  array( 0 => 64, 1 => 4 ),
  array( 0 => 64, 1 => 1 ),
  array( 0 => 65, 1 => 2 ),
  array( 0 => 65, 1 => 0 ),
  array( 0 => 63, 1 => 2 ),
  array( 0 => 63, 1 => 0 ),
  array( 0 => 66, 1 => 1 ),
  array( 0 => 66, 1 => 1 ),
  array( 0 => 66, 1 => 1 ),
  array( 0 => 66, 1 => 3 ),
  array( 0 => 66, 1 => 2 ),
  array( 0 => 67, 1 => 1 ),
  array( 0 => 67, 1 => 2 ),
  array( 0 => 67, 1 => 2 ),
  array( 0 => 70, 1 => 2 ),
  array( 0 => 69, 1 => 2 ),
  array( 0 => 72, 1 => 1 ),
  array( 0 => 72, 1 => 1 ),
  array( 0 => 72, 1 => 1 ),
  array( 0 => 68, 1 => 3 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 4 ),
  array( 0 => 68, 1 => 5 ),
  array( 0 => 68, 1 => 6 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 3 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 3 ),
  array( 0 => 68, 1 => 8 ),
  array( 0 => 80, 1 => 2 ),
  array( 0 => 80, 1 => 1 ),
  array( 0 => 68, 1 => 5 ),
  array( 0 => 68, 1 => 7 ),
  array( 0 => 68, 1 => 6 ),
  array( 0 => 68, 1 => 8 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 3 ),
  array( 0 => 68, 1 => 4 ),
  array( 0 => 66, 1 => 1 ),
  array( 0 => 68, 1 => 2 ),
  array( 0 => 68, 1 => 3 ),
  array( 0 => 68, 1 => 4 ),
  array( 0 => 68, 1 => 5 ),
  array( 0 => 73, 1 => 2 ),
  array( 0 => 73, 1 => 1 ),
  array( 0 => 73, 1 => 0 ),
  array( 0 => 83, 1 => 4 ),
  array( 0 => 83, 1 => 2 ),
  array( 0 => 83, 1 => 2 ),
  array( 0 => 83, 1 => 2 ),
  array( 0 => 83, 1 => 2 ),
  array( 0 => 83, 1 => 2 ),
  array( 0 => 83, 1 => 4 ),
  array( 0 => 79, 1 => 1 ),
  array( 0 => 79, 1 => 3 ),
  array( 0 => 78, 1 => 3 ),
  array( 0 => 78, 1 => 3 ),
  array( 0 => 78, 1 => 3 ),
  array( 0 => 78, 1 => 3 ),
  array( 0 => 76, 1 => 1 ),
  array( 0 => 76, 1 => 1 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 2 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 76, 1 => 3 ),
  array( 0 => 84, 1 => 7 ),
  array( 0 => 84, 1 => 7 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 2 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 1 ),
  array( 0 => 75, 1 => 3 ),
  array( 0 => 90, 1 => 1 ),
  array( 0 => 90, 1 => 1 ),
  array( 0 => 74, 1 => 1 ),
  array( 0 => 74, 1 => 1 ),
  array( 0 => 74, 1 => 3 ),
  array( 0 => 74, 1 => 1 ),
  array( 0 => 74, 1 => 3 ),
  array( 0 => 74, 1 => 4 ),
  array( 0 => 74, 1 => 3 ),
  array( 0 => 74, 1 => 4 ),
  array( 0 => 71, 1 => 2 ),
  array( 0 => 71, 1 => 2 ),
  array( 0 => 95, 1 => 2 ),
  array( 0 => 95, 1 => 0 ),
  array( 0 => 96, 1 => 2 ),
  array( 0 => 96, 1 => 2 ),
  array( 0 => 96, 1 => 4 ),
  array( 0 => 96, 1 => 2 ),
  array( 0 => 96, 1 => 2 ),
  array( 0 => 96, 1 => 4 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 5 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 3 ),
  array( 0 => 96, 1 => 2 ),
  array( 0 => 81, 1 => 1 ),
  array( 0 => 81, 1 => 1 ),
  array( 0 => 81, 1 => 2 ),
  array( 0 => 97, 1 => 1 ),
  array( 0 => 97, 1 => 1 ),
  array( 0 => 97, 1 => 3 ),
  array( 0 => 94, 1 => 2 ),
  array( 0 => 98, 1 => 1 ),
  array( 0 => 98, 1 => 2 ),
  array( 0 => 99, 1 => 3 ),
  array( 0 => 99, 1 => 3 ),
  array( 0 => 99, 1 => 5 ),
  array( 0 => 99, 1 => 6 ),
  array( 0 => 99, 1 => 2 ),
  array( 0 => 89, 1 => 4 ),
  array( 0 => 100, 1 => 4 ),
  array( 0 => 100, 1 => 4 ),
  array( 0 => 101, 1 => 3 ),
  array( 0 => 101, 1 => 1 ),
  array( 0 => 101, 1 => 0 ),
  array( 0 => 77, 1 => 3 ),
  array( 0 => 77, 1 => 2 ),
  array( 0 => 102, 1 => 3 ),
  array( 0 => 102, 1 => 2 ),
  array( 0 => 82, 1 => 2 ),
  array( 0 => 82, 1 => 0 ),
  array( 0 => 103, 1 => 2 ),
  array( 0 => 103, 1 => 3 ),
  array( 0 => 103, 1 => 2 ),
  array( 0 => 92, 1 => 1 ),
  array( 0 => 92, 1 => 2 ),
  array( 0 => 92, 1 => 1 ),
  array( 0 => 92, 1 => 2 ),
  array( 0 => 92, 1 => 3 ),
  array( 0 => 86, 1 => 1 ),
  array( 0 => 86, 1 => 1 ),
  array( 0 => 85, 1 => 1 ),
  array( 0 => 87, 1 => 1 ),
  array( 0 => 93, 1 => 3 ),
  array( 0 => 93, 1 => 3 ),
  array( 0 => 104, 1 => 1 ),
  array( 0 => 104, 1 => 3 ),
  array( 0 => 104, 1 => 0 ),
  array( 0 => 105, 1 => 3 ),
  array( 0 => 105, 1 => 3 ),
  array( 0 => 105, 1 => 1 ),
  array( 0 => 91, 1 => 2 ),
  array( 0 => 91, 1 => 3 ),
  array( 0 => 106, 1 => 2 ),
  array( 0 => 106, 1 => 1 ),
  array( 0 => 107, 1 => 3 ),
  array( 0 => 107, 1 => 3 ),
  array( 0 => 107, 1 => 1 ),
  array( 0 => 107, 1 => 3 ),
  array( 0 => 107, 1 => 3 ),
  array( 0 => 107, 1 => 1 ),
  array( 0 => 107, 1 => 1 ),
    );

    public static $yyReduceMap = array(
        0 => 0,
        1 => 1,
        2 => 2,
        3 => 3,
        4 => 4,
        5 => 5,
        6 => 6,
        21 => 6,
        22 => 6,
        23 => 6,
        36 => 6,
        56 => 6,
        57 => 6,
        65 => 6,
        66 => 6,
        77 => 6,
        82 => 6,
        83 => 6,
        88 => 6,
        92 => 6,
        93 => 6,
        97 => 6,
        98 => 6,
        100 => 6,
        105 => 6,
        169 => 6,
        174 => 6,
        7 => 7,
        8 => 8,
        9 => 9,
        11 => 11,
        12 => 12,
        13 => 13,
        14 => 14,
        15 => 15,
        16 => 16,
        17 => 17,
        18 => 18,
        19 => 19,
        20 => 20,
        24 => 24,
        25 => 25,
        26 => 26,
        27 => 27,
        28 => 28,
        29 => 29,
        30 => 30,
        31 => 31,
        33 => 31,
        32 => 32,
        34 => 34,
        35 => 35,
        37 => 37,
        38 => 38,
        39 => 39,
        40 => 40,
        41 => 41,
        42 => 42,
        43 => 43,
        44 => 44,
        45 => 45,
        46 => 46,
        47 => 47,
        48 => 48,
        49 => 49,
        50 => 50,
        59 => 50,
        147 => 50,
        151 => 50,
        155 => 50,
        157 => 50,
        51 => 51,
        148 => 51,
        154 => 51,
        52 => 52,
        53 => 53,
        54 => 53,
        55 => 55,
        132 => 55,
        58 => 58,
        60 => 60,
        61 => 61,
        62 => 61,
        63 => 63,
        64 => 64,
        67 => 67,
        68 => 68,
        69 => 68,
        70 => 70,
        71 => 71,
        72 => 72,
        73 => 73,
        74 => 74,
        75 => 75,
        76 => 76,
        78 => 78,
        80 => 78,
        81 => 78,
        112 => 78,
        79 => 79,
        84 => 84,
        85 => 85,
        86 => 86,
        87 => 87,
        89 => 89,
        90 => 90,
        91 => 90,
        94 => 94,
        95 => 95,
        96 => 96,
        99 => 99,
        101 => 101,
        102 => 102,
        103 => 103,
        104 => 104,
        106 => 106,
        107 => 107,
        108 => 108,
        109 => 109,
        110 => 110,
        111 => 111,
        113 => 113,
        171 => 113,
        114 => 114,
        115 => 115,
        116 => 116,
        117 => 117,
        118 => 118,
        119 => 119,
        127 => 119,
        120 => 120,
        121 => 121,
        122 => 122,
        123 => 122,
        125 => 122,
        126 => 122,
        124 => 124,
        128 => 128,
        129 => 129,
        130 => 130,
        175 => 130,
        131 => 131,
        133 => 133,
        134 => 134,
        135 => 135,
        136 => 136,
        137 => 137,
        138 => 138,
        139 => 139,
        140 => 140,
        141 => 141,
        142 => 142,
        143 => 143,
        144 => 144,
        145 => 145,
        146 => 146,
        149 => 149,
        150 => 150,
        152 => 152,
        153 => 153,
        156 => 156,
        158 => 158,
        159 => 159,
        160 => 160,
        161 => 161,
        162 => 162,
        163 => 163,
        164 => 164,
        165 => 165,
        166 => 166,
        167 => 167,
        168 => 167,
        170 => 170,
        172 => 172,
        173 => 173,
        176 => 176,
        177 => 177,
        178 => 178,
        179 => 179,
        182 => 179,
        180 => 180,
        183 => 180,
        181 => 181,
        184 => 184,
        185 => 185,
    );
// line 233 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r0(){
    $this->root_buffer->prepend_array($this, $this->template_prefix);
    $this->root_buffer->append_array($this, $this->template_postfix);
    $this->_retvalue = $this->root_buffer->to_smarty_php($this);
    }
// line 240 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r1(){
         $text = $this->yystack[ $this->yyidx + 0 ]->minor;

         if ((string)$text == '') {
            $this->current_buffer->append_subtree($this, null);
         }

         $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Text($text, $this->strip));
    }
// line 250 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r2(){
    $this->strip = true;
    }
// line 254 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r3(){
    $this->strip = false;
    }
// line 259 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r4(){
       $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Text($this->yystack[$this->yyidx + -1]->minor));
    }
// line 264 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r5(){
    $this->_retvalue = $this->yystack[$this->yyidx + -3]->minor.$this->yystack[$this->yyidx + -1]->minor;
    }
// line 267 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r6(){
    $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }
// line 271 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r7(){
        $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;

    }
// line 276 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r8(){
    $this->_retvalue = '';
    }
// line 280 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r9(){
      if ($this->compiler->has_code) {
          $this->current_buffer->append_subtree($this, $this->mergePrefixCode($this->yystack[$this->yyidx + 0]->minor));
      }
     $this->compiler->has_variable_string = false;
     $this->block_nesting_level = count($this->compiler->_tag_stack);
    }
// line 292 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r11(){
    $var = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->compiler->getLdelLength(), -$this->compiler->getRdelLength()), ' $');
    if (preg_match('/^(.*)(\s+nocache)$/', $var, $match)) {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression',array('nocache'),array('value'=>$this->compiler->compileVariable('\''.$match[1].'\'')));
    } else {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression',array(),array('value'=>$this->compiler->compileVariable('\''.$var.'\'')));
    }
    }
// line 302 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r12(){
    $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->compiler->getLdelLength(), -$this->compiler->getRdelLength()));
    if ($tag == 'strip') {
        $this->strip = true;
        $this->_retvalue = null;
    } else {
        if (defined($tag)) {
            if ($this->security) {
               $this->security->isTrustedConstant($tag, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression',array(),array('value'=>$tag));
        } else {
            if (preg_match('/^(.*)(\s+nocache)$/', $tag, $match)) {
                $this->_retvalue = $this->compiler->compileTag($match[1],array('\'nocache\''));
            } else {
                $this->_retvalue = $this->compiler->compileTag($tag,array());
            }
        }
    }
    }
// line 323 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r13(){
    $j = strrpos($this->yystack[$this->yyidx + 0]->minor,'.');
    if ($this->yystack[$this->yyidx + 0]->minor[$j+1] == 'c') {
        // {$smarty.block.child}
        $this->_retvalue = $this->compiler->compileTag('child',array(),array($this->yystack[$this->yyidx + 0]->minor));
    } else {
        // {$smarty.block.parent}
       $this->_retvalue = $this->compiler->compileTag('parent',array(),array($this->yystack[$this->yyidx + 0]->minor));
     }
    }
// line 334 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r14(){
    $this->_retvalue  = $this->yystack[$this->yyidx + -1]->minor;
    }
// line 338 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r15(){
     $this->_retvalue  = $this->yystack[$this->yyidx + -1]->minor;
     }
// line 342 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r16(){
    $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + 0]->minor[1],array('value'=>$this->yystack[$this->yyidx + 0]->minor[0]));
    }
// line 351 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r17(){
    $this->_retvalue = $this->compiler->compileTag('assign',array_merge(array(array('value'=>$this->yystack[$this->yyidx + 0]->minor[0]),array('var'=>'\''.substr($this->yystack[$this->yyidx + -1]->minor,1).'\'')),$this->yystack[$this->yyidx + 0]->minor[1]));
    }
// line 355 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r18(){
    $this->_retvalue = $this->compiler->compileTag('assign',array_merge(array(array('value'=>$this->yystack[$this->yyidx + 0]->minor[0]),array('var'=>$this->yystack[$this->yyidx + -1]->minor['var'])),$this->yystack[$this->yyidx + 0]->minor[1]),array('smarty_internal_index'=>$this->yystack[$this->yyidx + -1]->minor['smarty_internal_index']));
    }
// line 359 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r19(){
       $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }
// line 363 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r20(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor);
    }
// line 378 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r24(){
        if (defined($this->yystack[$this->yyidx + -1]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + -1]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + 0]->minor,array('value'=>$this->yystack[$this->yyidx + -1]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor);
        }
    }
// line 388 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r25(){
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression',array(),array('value'=>$this->yystack[$this->yyidx + 0]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor,array());
        }
    }
// line 401 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r26(){
        if (defined($this->yystack[$this->yyidx + -2]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + -2]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + 0]->minor,array('value'=>$this->yystack[$this->yyidx + -2]->minor, 'modifierlist'=>$this->yystack[$this->yyidx + -1]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -2]->minor,$this->yystack[$this->yyidx + 0]->minor, array('modifierlist'=>$this->yystack[$this->yyidx + -1]->minor));
        }
    }
// line 413 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r27(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -3]->minor,$this->yystack[$this->yyidx + 0]->minor,array('object_method'=>$this->yystack[$this->yyidx + -1]->minor));
    }
// line 418 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r28(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -4]->minor,$this->yystack[$this->yyidx + 0]->minor,array('modifierlist'=>$this->yystack[$this->yyidx + -1]->minor, 'object_method'=>$this->yystack[$this->yyidx + -2]->minor));
    }
// line 423 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r29(){
    $this->_retvalue = $this->compiler->compileTag('make_nocache',array(array('var'=>'\''.substr($this->yystack[$this->yyidx + 0]->minor,1).'\'')));
    }
// line 428 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r30(){
    $tag = trim(substr($this->yystack[$this->yyidx + -1]->minor,$this->compiler->getLdelLength())); 
    $this->_retvalue = $this->compiler->compileTag(($tag === 'else if')? 'elseif' : $tag,array(),array('if condition'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 433 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r31(){
    $tag = trim(substr($this->yystack[$this->yyidx + -2]->minor,$this->compiler->getLdelLength()));
    $this->_retvalue = $this->compiler->compileTag(($tag === 'else if')? 'elseif' : $tag,$this->yystack[$this->yyidx + 0]->minor,array('if condition'=>$this->yystack[$this->yyidx + -1]->minor));
    }
// line 438 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r32(){
    $tag = trim(substr($this->yystack[$this->yyidx + -1]->minor,$this->compiler->getLdelLength()));
    $this->_retvalue = $this->compiler->compileTag(($tag === 'else if')? 'elseif' : $tag,array(),array('if condition'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 449 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r34(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + 0]->minor,array(array('start'=>$this->yystack[$this->yyidx + -6]->minor),array('ifexp'=>$this->yystack[$this->yyidx + -4]->minor),array('var'=>$this->yystack[$this->yyidx + -2]->minor),array('step'=>$this->yystack[$this->yyidx + -1]->minor))),1);
    }
// line 453 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r35(){
    $this->_retvalue = '='.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 461 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r37(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + 0]->minor,array(array('start'=>$this->yystack[$this->yyidx + -3]->minor),array('to'=>$this->yystack[$this->yyidx + -1]->minor))),0);
    }
// line 465 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r38(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + 0]->minor,array(array('start'=>$this->yystack[$this->yyidx + -5]->minor),array('to'=>$this->yystack[$this->yyidx + -3]->minor),array('step'=>$this->yystack[$this->yyidx + -1]->minor))),0);
    }
// line 470 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r39(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + 0]->minor,array(array('from'=>$this->yystack[$this->yyidx + -3]->minor),array('item'=>$this->yystack[$this->yyidx + -1]->minor))));
    }
// line 474 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r40(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + 0]->minor,array(array('from'=>$this->yystack[$this->yyidx + -5]->minor),array('item'=>$this->yystack[$this->yyidx + -1]->minor),array('key'=>$this->yystack[$this->yyidx + -3]->minor))));
    }
// line 477 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r41(){
    $this->_retvalue = $this->compiler->compileTag('foreach',$this->yystack[$this->yyidx + 0]->minor);
    }
// line 482 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r42(){
    $this->_retvalue = $this->compiler->compileTag('setfilter',array(),array('modifier_list'=>array(array_merge(array($this->yystack[$this->yyidx + -1]->minor),$this->yystack[$this->yyidx + 0]->minor))));
    }
// line 486 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r43(){
    $this->_retvalue = $this->compiler->compileTag('setfilter',array(),array('modifier_list'=>array_merge(array(array_merge(array($this->yystack[$this->yyidx + -2]->minor),$this->yystack[$this->yyidx + -1]->minor)),$this->yystack[$this->yyidx + 0]->minor)));
    }
// line 492 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r44(){
    $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->compiler->getLdelLength(), -$this->compiler->getRdelLength()), ' /');
    if ($tag === 'strip') {
        $this->strip = false;
        $this->_retvalue = null;
    } else {
       $this->_retvalue = $this->compiler->compileTag($tag.'close',array());
    }
     }
// line 501 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r45(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor.'close',array());
    }
// line 505 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r46(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -1]->minor.'close',array(),array('modifier_list'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 510 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r47(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -2]->minor.'close',array(),array('object_method'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 514 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r48(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -3]->minor.'close',array(),array('object_method'=>$this->yystack[$this->yyidx + -1]->minor, 'modifier_list'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 522 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r49(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    $this->_retvalue[] = $this->yystack[$this->yyidx + 0]->minor;
    }
// line 528 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r50(){
    $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }
// line 533 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r51(){
    $this->_retvalue = array();
    }
// line 538 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r52(){
    if (defined($this->yystack[$this->yyidx + 0]->minor)) {
        if ($this->security) {
            $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
        }
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>$this->yystack[$this->yyidx + 0]->minor);
    } else {
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>'\''.$this->yystack[$this->yyidx + 0]->minor.'\'');
    }
    }
// line 549 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r53(){
    $this->_retvalue = array(trim($this->yystack[$this->yyidx + -1]->minor," =\n\r\t")=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 557 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r55(){
    $this->_retvalue = '\''.$this->yystack[$this->yyidx + 0]->minor.'\'';
    }
// line 569 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r58(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 582 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r60(){
    $this->yystack[$this->yyidx + -2]->minor[]=$this->yystack[$this->yyidx + 0]->minor;
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor;
    }
// line 587 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r61(){
    $this->_retvalue = array('var' => '\''.substr($this->yystack[$this->yyidx + -2]->minor,1).'\'', 'value'=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 594 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r63(){
    $this->_retvalue = array('var' => $this->yystack[$this->yyidx + -2]->minor, 'value'=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 598 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r64(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    }
// line 618 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r67(){
    $this->_retvalue = '$_smarty_tpl->getStreamVariable(\''.substr($this->yystack[$this->yyidx + -2]->minor,1).'://' . $this->yystack[$this->yyidx + 0]->minor . '\')';
    }
// line 623 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r68(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor . trim($this->yystack[$this->yyidx + -1]->minor) . $this->yystack[$this->yyidx + 0]->minor;
    }
// line 633 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r70(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor['pre']. $this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor['op'].$this->yystack[$this->yyidx + 0]->minor .')';
    }
// line 637 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r71(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 641 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r72(){
    $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor . $this->yystack[$this->yyidx + -1]->minor . ')';
    }
// line 645 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r73(){
    $this->_retvalue = 'in_array('.$this->yystack[$this->yyidx + -2]->minor.','.$this->yystack[$this->yyidx + 0]->minor.')';
    }
// line 649 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r74(){
    $this->_retvalue = 'in_array('.$this->yystack[$this->yyidx + -2]->minor.',(array)'.$this->yystack[$this->yyidx + 0]->minor.')';
    }
// line 657 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r75(){
    $this->_retvalue = $this->yystack[$this->yyidx + -5]->minor.' ? '. $this->compiler->compileVariable('\''.substr($this->yystack[$this->yyidx + -2]->minor,1).'\'') . ' : '.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 661 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r76(){
    $this->_retvalue = $this->yystack[$this->yyidx + -5]->minor.' ? '.$this->yystack[$this->yyidx + -2]->minor.' : '.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 671 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r78(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 676 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r79(){
    $this->_retvalue = '!'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 697 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r84(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'.'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 701 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r85(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.'.';
    }
// line 705 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r86(){
    $this->_retvalue = '.'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 710 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r87(){
    if (defined($this->yystack[$this->yyidx + 0]->minor)) {
        if ($this->security) {
             $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
        }
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    } else {
        $this->_retvalue = '\''.$this->yystack[$this->yyidx + 0]->minor.'\'';
    }
    }
// line 727 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r89(){
    $this->_retvalue = '('. $this->yystack[$this->yyidx + -1]->minor .')';
    }
// line 731 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r90(){
      $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 749 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r94(){
	    if ($this->security && $this->security->static_classes !== array()) {
		    $this->compiler->trigger_template_error('dynamic static class not allowed by security setting');
	    }
    $prefixVar = $this->compiler->getNewPrefixVariable();
    if ($this->yystack[$this->yyidx + -2]->minor['var'] === '\'smarty\'') {
        $this->compiler->appendPrefixCode("<?php {$prefixVar} = ". $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + -2]->minor['smarty_internal_index']).';?>');
     } else {
        $this->compiler->appendPrefixCode("<?php  {$prefixVar} = ". $this->compiler->compileVariable($this->yystack[$this->yyidx + -2]->minor['var']).$this->yystack[$this->yyidx + -2]->minor['smarty_internal_index'].';?>');
    }
    $this->_retvalue = $prefixVar .'::'.$this->yystack[$this->yyidx + 0]->minor[0].$this->yystack[$this->yyidx + 0]->minor[1];
    }
// line 760 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r95(){
    $prefixVar = $this->compiler->getNewPrefixVariable();
    $tmp = $this->compiler->appendCode('<?php ob_start();?>', $this->yystack[$this->yyidx + 0]->minor);
    $this->compiler->appendPrefixCode($this->compiler->appendCode($tmp, "<?php {$prefixVar} = ob_get_clean();?>"));
    $this->_retvalue = $prefixVar;
    }
// line 767 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r96(){
    $this->_retvalue = $this->compiler->compileTag('private_modifier',array(),array('value'=>$this->yystack[$this->yyidx + -1]->minor,'modifierlist'=>$this->yystack[$this->yyidx + 0]->minor));
    }
// line 780 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r99(){
    if (!in_array(strtolower($this->yystack[$this->yyidx + -2]->minor), array('self', 'parent')) && (!$this->security || $this->security->isTrustedStaticClassAccess($this->yystack[$this->yyidx + -2]->minor, $this->yystack[$this->yyidx + 0]->minor, $this->compiler))) {
        if (isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + -2]->minor])) {
            $this->_retvalue = $this->smarty->registered_classes[$this->yystack[$this->yyidx + -2]->minor].'::'.$this->yystack[$this->yyidx + 0]->minor[0].$this->yystack[$this->yyidx + 0]->minor[1];
        } else {
            $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'::'.$this->yystack[$this->yyidx + 0]->minor[0].$this->yystack[$this->yyidx + 0]->minor[1];
        } 
    } else {
        $this->compiler->trigger_template_error ('static class \''.$this->yystack[$this->yyidx + -2]->minor.'\' is undefined or not allowed by security setting');
    }
    }
// line 799 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r101(){
    $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
        }
// line 810 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r102(){
   $this->_retvalue = $this->compiler->compileVariable('\''.substr($this->yystack[$this->yyidx + 0]->minor,1).'\'');
    }
// line 813 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r103(){
    if ($this->yystack[$this->yyidx + 0]->minor['var'] === '\'smarty\'') {
        $smarty_var = $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + 0]->minor['smarty_internal_index']);
        $this->_retvalue = $smarty_var;
    } else {
        // used for array reset,next,prev,end,current 
        $this->last_variable = $this->yystack[$this->yyidx + 0]->minor['var'];
        $this->last_index = $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
        $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor['var']).$this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
    }
    }
// line 826 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r104(){
    $this->_retvalue = '$_smarty_tpl->tpl_vars['. $this->yystack[$this->yyidx + -2]->minor .']->'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 836 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r106(){
    $this->_retvalue = $this->compiler->compileConfigVariable('\'' . $this->yystack[$this->yyidx + -1]->minor . '\'');
    }
// line 840 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r107(){
    $this->_retvalue = '(is_array($tmp = ' . $this->compiler->compileConfigVariable('\'' . $this->yystack[$this->yyidx + -2]->minor . '\'') . ') ? $tmp'.$this->yystack[$this->yyidx + 0]->minor.' :null)';
    }
// line 844 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r108(){
    $this->_retvalue = $this->compiler->compileConfigVariable($this->yystack[$this->yyidx + -1]->minor);
    }
// line 848 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r109(){
    $this->_retvalue = '(is_array($tmp = ' . $this->compiler->compileConfigVariable($this->yystack[$this->yyidx + -2]->minor) . ') ? $tmp'.$this->yystack[$this->yyidx + 0]->minor.' : null)';
    }
// line 852 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r110(){
    $this->_retvalue = array('var'=>'\''.substr($this->yystack[$this->yyidx + -1]->minor,1).'\'', 'smarty_internal_index'=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 855 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r111(){
    $this->_retvalue = array('var'=>$this->yystack[$this->yyidx + -1]->minor, 'smarty_internal_index'=>$this->yystack[$this->yyidx + 0]->minor);
    }
// line 868 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r113(){
    return;
    }
// line 874 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r114(){
    $this->_retvalue = '['.$this->compiler->compileVariable('\''.substr($this->yystack[$this->yyidx + 0]->minor,1).'\'').']';
    }
// line 877 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r115(){
    $this->_retvalue = '['.$this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor).']';
    }
// line 881 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r116(){
    $this->_retvalue = '['.$this->compiler->compileVariable($this->yystack[$this->yyidx + -2]->minor).'->'.$this->yystack[$this->yyidx + 0]->minor.']';
    }
// line 885 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r117(){
    $this->_retvalue = '[\''. $this->yystack[$this->yyidx + 0]->minor .'\']';
    }
// line 889 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r118(){
    $this->_retvalue = '['. $this->yystack[$this->yyidx + 0]->minor .']';
    }
// line 894 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r119(){
    $this->_retvalue = '['. $this->yystack[$this->yyidx + -1]->minor .']';
    }
// line 899 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r120(){
    $this->_retvalue = '['.$this->compiler->compileTag('private_special_variable',array(),'[\'section\'][\''.$this->yystack[$this->yyidx + -1]->minor.'\'][\'index\']').']';
    }
// line 903 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r121(){
    $this->_retvalue = '['.$this->compiler->compileTag('private_special_variable',array(),'[\'section\'][\''.$this->yystack[$this->yyidx + -3]->minor.'\'][\''.$this->yystack[$this->yyidx + -1]->minor.'\']').']';
    }
// line 906 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r122(){
    $this->_retvalue = '['.$this->yystack[$this->yyidx + -1]->minor.']';
    }
// line 912 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r124(){
    $this->_retvalue = '['.$this->compiler->compileVariable('\''.substr($this->yystack[$this->yyidx + -1]->minor,1).'\'').']';
    }
// line 928 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r128(){
    $this->_retvalue = '[]';
    }
// line 938 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r129(){
    $this->_retvalue = '\''.substr($this->yystack[$this->yyidx + 0]->minor,1).'\'';
    }
// line 942 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r130(){
    $this->_retvalue = '\'\'';
    }
// line 947 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r131(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.'.'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 955 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r133(){
    $var = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->compiler->getLdelLength(), -$this->compiler->getRdelLength()), ' $');
    $this->_retvalue = $this->compiler->compileVariable('\''.$var.'\'');
    }
// line 961 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r134(){
    $this->_retvalue = '('.$this->yystack[$this->yyidx + -1]->minor.')';
    }
// line 968 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r135(){
    if ($this->yystack[$this->yyidx + -1]->minor['var'] === '\'smarty\'') {
        $this->_retvalue =  $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + -1]->minor['smarty_internal_index']).$this->yystack[$this->yyidx + 0]->minor;
    } else {
        $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + -1]->minor['var']).$this->yystack[$this->yyidx + -1]->minor['smarty_internal_index'].$this->yystack[$this->yyidx + 0]->minor;
    }
    }
// line 977 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r136(){
    $this->_retvalue  = $this->yystack[$this->yyidx + 0]->minor;
    }
// line 982 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r137(){
    $this->_retvalue  = $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 987 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r138(){
    if ($this->security && substr($this->yystack[$this->yyidx + -1]->minor,0,1) === '_') {
        $this->compiler->trigger_template_error (self::ERR1);
    }
    $this->_retvalue = '->'.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 994 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r139(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::ERR2);
    }
    $this->_retvalue = '->{'.$this->compiler->compileVariable($this->yystack[$this->yyidx + -1]->minor).$this->yystack[$this->yyidx + 0]->minor.'}';
    }
// line 1001 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r140(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::ERR2);
    }
    $this->_retvalue = '->{'.$this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + 0]->minor.'}';
    }
// line 1008 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r141(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::ERR2);
    }
    $this->_retvalue = '->{\''.$this->yystack[$this->yyidx + -4]->minor.'\'.'.$this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + 0]->minor.'}';
    }
// line 1016 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r142(){
    $this->_retvalue = '->'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 1024 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r143(){
    $this->_retvalue = $this->compiler->compilePHPFunctionCall($this->yystack[$this->yyidx + -3]->minor, $this->yystack[$this->yyidx + -1]->minor);
    }
// line 1032 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r144(){
    if ($this->security && substr($this->yystack[$this->yyidx + -3]->minor,0,1) === '_') {
        $this->compiler->trigger_template_error (self::ERR1);
    }
    $this->_retvalue = $this->yystack[$this->yyidx + -3]->minor . '('. implode(',',$this->yystack[$this->yyidx + -1]->minor) .')';
    }
// line 1039 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r145(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::ERR2);
    }
    $prefixVar = $this->compiler->getNewPrefixVariable();
    $this->compiler->appendPrefixCode("<?php {$prefixVar} = ".$this->compiler->compileVariable('\''.substr($this->yystack[$this->yyidx + -3]->minor,1).'\'').';?>');
    $this->_retvalue = $prefixVar .'('. implode(',',$this->yystack[$this->yyidx + -1]->minor) .')';
    }
// line 1050 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r146(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -2]->minor,array($this->yystack[$this->yyidx + 0]->minor));
    }
// line 1067 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r149(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -2]->minor,array(array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor)));
    }
// line 1071 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r150(){
    $this->_retvalue = array(array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor));
    }
// line 1079 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r152(){
    $this->_retvalue =  array($this->yystack[$this->yyidx + 0]->minor);
    }
// line 1087 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r153(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor);
    }
// line 1100 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r156(){
    $this->_retvalue = array(trim($this->yystack[$this->yyidx + -1]->minor).$this->yystack[$this->yyidx + 0]->minor);
    }
// line 1109 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r158(){
    $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '', 'method');
    }
// line 1114 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r159(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -1]->minor, $this->yystack[$this->yyidx + 0]->minor, 'method');
    }
// line 1119 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r160(){
    $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '');
    }
// line 1124 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r161(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -1]->minor, $this->yystack[$this->yyidx + 0]->minor, 'property');
    }
// line 1129 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r162(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor, $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor, 'property');
    }
// line 1135 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r163(){
    $this->_retvalue = ' '. trim($this->yystack[$this->yyidx + 0]->minor) . ' ';
    }
// line 1139 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r164(){
    static $lops = array(
        'eq' => ' == ',
        'ne' => ' != ',
        'neq' => ' != ',
        'gt' => ' > ',
        'ge' => ' >= ',
        'gte' => ' >= ',
        'lt' =>  ' < ',
        'le' =>  ' <= ',
        'lte' => ' <= ',
        'mod' =>  ' % ',
        'and' => ' && ',
        'or' => ' || ',
        'xor' => ' xor ',
         );
    $op = strtolower(preg_replace('/\s*/', '', $this->yystack[$this->yyidx + 0]->minor));
    $this->_retvalue = $lops[$op];
    }
// line 1158 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r165(){
     static $tlops = array(
         'isdivby' => array('op' => ' % ', 'pre' => '!('),
         'isnotdivby' => array('op' => ' % ', 'pre' => '('),
         'isevenby' => array('op' => ' / ', 'pre' => '!(1 & '),
         'isnotevenby' => array('op' => ' / ', 'pre' => '(1 & '),
         'isoddby' => array('op' => ' / ', 'pre' => '(1 & '),
         'isnotoddby' => array('op' => ' / ', 'pre' => '!(1 & '),
         );
     $op = strtolower(preg_replace('/\s*/', '', $this->yystack[$this->yyidx + 0]->minor));
     $this->_retvalue = $tlops[$op];
     }
// line 1171 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r166(){
        static $scond = array (
            'iseven' => '!(1 & ',
            'isnoteven' => '(1 & ',
            'isodd' => '(1 & ',
            'isnotodd' => '!(1 & ',
        );
   $op = strtolower(str_replace(' ', '', $this->yystack[$this->yyidx + 0]->minor));
   $this->_retvalue = $scond[$op];
    }
// line 1185 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r167(){
    $this->_retvalue = 'array('.$this->yystack[$this->yyidx + -1]->minor.')';
    }
// line 1196 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r170(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.','.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 1204 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r172(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'=>'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 1208 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r173(){ 
    $this->_retvalue = '\''.$this->yystack[$this->yyidx + -2]->minor.'\'=>'.$this->yystack[$this->yyidx + 0]->minor;
    }
// line 1224 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r176(){
    $this->compiler->leaveDoubleQuote();
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor->to_smarty_php($this);
    }
// line 1230 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r177(){
    $this->yystack[$this->yyidx + -1]->minor->append_subtree($this, $this->yystack[$this->yyidx + 0]->minor);
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    }
// line 1235 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r178(){
    $this->_retvalue = new Smarty_Internal_ParseTree_Dq($this, $this->yystack[$this->yyidx + 0]->minor);
    }
// line 1239 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r179(){
    $this->_retvalue = new Smarty_Internal_ParseTree_Code('(string)'.$this->yystack[$this->yyidx + -1]->minor);
    }
// line 1243 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r180(){
    $this->_retvalue = new Smarty_Internal_ParseTree_Code('(string)('.$this->yystack[$this->yyidx + -1]->minor.')');
    }
// line 1247 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r181(){
    $this->_retvalue = new Smarty_Internal_ParseTree_Code('(string)$_smarty_tpl->tpl_vars[\''. substr($this->yystack[$this->yyidx + 0]->minor,1) .'\']->value');
    }
// line 1259 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r184(){
    $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->yystack[$this->yyidx + 0]->minor);
    }
// line 1263 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r185(){
    $this->_retvalue = new Smarty_Internal_ParseTree_DqContent($this->yystack[$this->yyidx + 0]->minor);
    }

    private $_retvalue;

    public function yy_reduce($yyruleno)
    {
        if ($this->yyTraceFILE && $yyruleno >= 0
              && $yyruleno < count(self::$yyRuleName)) {
            fprintf($this->yyTraceFILE, "%sReduce (%d) [%s].\n",
                $this->yyTracePrompt, $yyruleno,
                self::$yyRuleName[$yyruleno]);
        }

        $this->_retvalue = $yy_lefthand_side = null;
        if (isset(self::$yyReduceMap[$yyruleno])) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[$yyruleno]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[$yyruleno][0];
        $yysize = self::$yyRuleInfo[$yyruleno][1];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i--) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[$this->yyidx] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact === self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        } while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
// line 213 "../smarty/lexer/smarty_internal_templateparser.y"

    $this->internalError = true;
    $this->yymajor = $yymajor;
    $this->compiler->trigger_template_error();
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        } while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
// line 206 "../smarty/lexer/smarty_internal_templateparser.y"

    $this->successful = !$this->internalError;
    $this->internalError = false;
    $this->retvalue = $this->_retvalue;
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */

        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = -1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            $this->yystack[] = $x;
        }
        $yyendofinput = ($yymajor==0);

        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sInput %s\n",
                $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
        }

        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL &&
                  !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt--;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact === self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf($this->yyTraceFILE, "%sSyntax Error!\n",
                        $this->yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[$this->yyidx]->major;
                    if ($yymx === self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf($this->yyTraceFILE, "%sDiscard input token %s\n",
                                $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 &&
                                 $yymx !== self::YYERRORSYMBOL &&
        ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE
                              ){
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor==0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx !== self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor !== self::YYNOCODE && $this->yyidx >= 0);
    }
}

