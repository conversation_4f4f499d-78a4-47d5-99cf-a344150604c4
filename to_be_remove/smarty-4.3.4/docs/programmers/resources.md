Resources
=========

The templates may come from a variety of sources. When you
[`display()`](./api-functions/api-display.md) or [`fetch()`](./api-functions/api-fetch.md) a template, or
when you include a template from within another template, you supply a
resource type, followed by the appropriate path and template name. If a
resource is not explicitly given, the value of
[`$default_resource_type`](./api-variables/variable-default-resource-type.md) (default:
\"file\") is assumed.

## Table of contents

- [File Template Resources](./resources/resources-file.md)
- [String Template Resources](./resources/resources-string.md)
- [Stream Template Resources](./resources/resources-streams.md)
- [Extends Template Resources](./resources/resources-extends.md)
- [Custom Template Resources](./resources/resources-custom.md)

