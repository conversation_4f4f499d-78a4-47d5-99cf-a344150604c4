<?php
/**
 * 自動修復未定義常量警告的腳本
 * 
 * 這個腳本會掃描指定目錄下的所有 PHP 文件，
 * 查找並修復常見的未定義常量警告問題。
 */

// 設置要掃描的目錄
$scanDir = __DIR__;

// 要忽略的目錄
$ignoreDirs = [
    'libs/smarty4', // Smarty 庫文件不需要修改
    'phpMyAdmin',   // phpMyAdmin 不需要修改
];

// 要處理的文件擴展名
$fileExtensions = ['php'];

// 計數器
$totalFiles = 0;
$modifiedFiles = 0;
$totalReplacements = 0;

// 正則表達式模式
$patterns = [
    // $array['key'] -> $array['key']
    '/\$([a-zA-Z_][a-zA-Z0-9_]*)\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$' . $matches[1] . '[\'' . $matches[2] . '\']';
    },
    
    // $_POST['key'] -> $_POST['key']
    '/\$_POST\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$_POST[\'' . $matches[1] . '\']';
    },
    
    // $_GET['key'] -> $_GET['key']
    '/\$_GET\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$_GET[\'' . $matches[1] . '\']';
    },
    
    // $_SESSION['key'] -> $_SESSION['key']
    '/\$_SESSION\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$_SESSION[\'' . $matches[1] . '\']';
    },
    
    // $lang['key'] -> $lang['key']
    '/\$lang\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$lang[\'' . $matches[1] . '\']';
    },
    
    // $GameInfo['key'] -> $GameInfo['key']
    '/\$GameInfo\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$GameInfo[\'' . $matches[1] . '\']';
    },
    
    // $gameinfo['key'] -> $gameinfo['key']
    '/\$gameinfo\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$gameinfo[\'' . $matches[1] . '\']';
    },
    
    // $row['key'] -> $row['key']
    '/\$row\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$row[\'' . $matches[1] . '\']';
    },
    
    // $rs['key'] -> $rs['key']
    '/\$rs\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$rs[\'' . $matches[1] . '\']';
    },
    
    // $_attr['key'] -> $_attr['key']
    '/\$_attr\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$_attr[\'' . $matches[1] . '\']';
    },
    
    // $_statement['key'] -> $_statement['key']
    '/\$_statement\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$_statement[\'' . $matches[1] . '\']';
    },
    
    // $centd['key'] -> $centd['key']
    '/\$centd\[([a-zA-Z_][a-zA-Z0-9_]*)\]/' => function($matches) {
        return '$centd[\'' . $matches[1] . '\']';
    },
];

/**
 * 掃描目錄中的所有 PHP 文件
 */
function scanDirectory($dir, $ignoreDirs, $fileExtensions) {
    global $totalFiles, $modifiedFiles, $totalReplacements;
    
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') {
            continue;
        }
        
        $path = $dir . '/' . $file;
        
        // 檢查是否為忽略的目錄
        $shouldIgnore = false;
        foreach ($ignoreDirs as $ignoreDir) {
            if (strpos($path, $ignoreDir) !== false) {
                $shouldIgnore = true;
                break;
            }
        }
        
        if ($shouldIgnore) {
            continue;
        }
        
        if (is_dir($path)) {
            scanDirectory($path, $ignoreDirs, $fileExtensions);
        } else {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if (in_array($extension, $fileExtensions)) {
                $totalFiles++;
                processFile($path);
            }
        }
    }
}

/**
 * 處理單個文件
 */
function processFile($filePath) {
    global $patterns, $modifiedFiles, $totalReplacements;
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    $replacements = 0;
    
    // 應用所有正則表達式模式
    foreach ($patterns as $pattern => $replacement) {
        $newContent = preg_replace_callback($pattern, $replacement, $content);
        
        // 計算替換次數
        $count = substr_count($newContent, "'") - substr_count($content, "'");
        $replacements += $count / 2; // 每次替換會增加兩個引號
        
        $content = $newContent;
    }
    
    // 如果內容有變化，則寫回文件
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        $modifiedFiles++;
        $totalReplacements += $replacements;
        
        echo "已修改文件: $filePath (替換了 $replacements 處)\n";
    }
}

// 開始掃描
echo "開始掃描目錄: $scanDir\n";
scanDirectory($scanDir, $ignoreDirs, $fileExtensions);
echo "掃描完成。處理了 $totalFiles 個文件，修改了 $modifiedFiles 個文件，共替換了 $totalReplacements 處未定義常量。\n";