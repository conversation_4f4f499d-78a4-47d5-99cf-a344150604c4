<?php
/**
 * 服務器狀態調試腳本
 * 用於檢查模板編譯、緩存狀態等
 */

// 設置錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once 'inc/maininit.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服務器狀態調試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error-box {
            background: #ffe7e7;
            border: 1px solid #ffb3b3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success-box {
            background: #e7ffe7;
            border: 1px solid #b3ffb3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>服務器狀態調試</h1>
        <p>檢查時間: <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="status-box">
            <h3>PHP 環境信息</h3>
            <table>
                <tr><th>項目</th><th>值</th></tr>
                <tr><td>PHP 版本</td><td><?php echo PHP_VERSION; ?></td></tr>
                <tr><td>服務器軟件</td><td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></td></tr>
                <tr><td>文檔根目錄</td><td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? '未知'; ?></td></tr>
                <tr><td>當前腳本路徑</td><td><?php echo __FILE__; ?></td></tr>
            </table>
        </div>
        
        <div class="status-box">
            <h3>Smarty 狀態</h3>
            <table>
                <tr><th>項目</th><th>值</th></tr>
                <tr><td>Smarty 版本</td><td><?php echo $smarty::SMARTY_VERSION ?? '未知'; ?></td></tr>
                <tr><td>模板目錄</td><td><?php echo implode(', ', $smarty->getTemplateDir()); ?></td></tr>
                <tr><td>編譯目錄</td><td><?php echo $smarty->getCompileDir(); ?></td></tr>
                <tr><td>緩存目錄</td><td><?php echo $smarty->getCacheDir(); ?></td></tr>
                <tr><td>強制編譯</td><td><?php echo $smarty->force_compile ? '是' : '否'; ?></td></tr>
                <tr><td>緩存啟用</td><td><?php echo $smarty->caching ? '是' : '否'; ?></td></tr>
            </table>
        </div>
        
        <div class="status-box">
            <h3>目錄權限檢查</h3>
            <?php
            $directories = [
                'templates' => 'templates/',
                'templates_c' => 'templates_c/',
                'cache' => 'cache/',
            ];
            
            echo '<table>';
            echo '<tr><th>目錄</th><th>存在</th><th>可讀</th><th>可寫</th><th>權限</th></tr>';
            
            foreach ($directories as $name => $path) {
                $exists = is_dir($path);
                $readable = $exists ? is_readable($path) : false;
                $writable = $exists ? is_writable($path) : false;
                $perms = $exists ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A';
                
                echo '<tr>';
                echo '<td>' . $name . '</td>';
                echo '<td>' . ($exists ? '是' : '否') . '</td>';
                echo '<td>' . ($readable ? '是' : '否') . '</td>';
                echo '<td>' . ($writable ? '是' : '否') . '</td>';
                echo '<td>' . $perms . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            ?>
        </div>
        
        <div class="status-box">
            <h3>模板文件檢查</h3>
            <?php
            $templateFile = 'templates/GameManage.tpl';
            if (file_exists($templateFile)) {
                $fileSize = filesize($templateFile);
                $lastModified = date('Y-m-d H:i:s', filemtime($templateFile));
                $isReadable = is_readable($templateFile);
                
                echo '<table>';
                echo '<tr><th>項目</th><th>值</th></tr>';
                echo '<tr><td>文件存在</td><td>是</td></tr>';
                echo '<tr><td>文件大小</td><td>' . number_format($fileSize) . ' 字節</td></tr>';
                echo '<tr><td>最後修改</td><td>' . $lastModified . '</td></tr>';
                echo '<tr><td>可讀</td><td>' . ($isReadable ? '是' : '否') . '</td></tr>';
                echo '</table>';
                
                // 檢查文件中是否包含修復代碼
                $content = file_get_contents($templateFile);
                $hasFixCode = strpos($content, '地圖選擇修復版本') !== false;
                $hasUserSelected = strpos($content, 'user-selected') !== false;
                
                echo '<h4>修復代碼檢查</h4>';
                echo '<table>';
                echo '<tr><td>包含修復標記</td><td>' . ($hasFixCode ? '是' : '否') . '</td></tr>';
                echo '<tr><td>包含用戶選擇邏輯</td><td>' . ($hasUserSelected ? '是' : '否') . '</td></tr>';
                echo '</table>';
            } else {
                echo '<div class="error-box">模板文件不存在: ' . $templateFile . '</div>';
            }
            ?>
        </div>
        
        <div class="status-box">
            <h3>編譯文件檢查</h3>
            <?php
            $compileDir = $smarty->getCompileDir();
            if (is_dir($compileDir)) {
                $files = glob($compileDir . '*');
                echo '<p>編譯目錄中的文件數量: ' . count($files) . '</p>';
                
                if (count($files) > 0) {
                    echo '<h4>最近的編譯文件:</h4>';
                    echo '<table>';
                    echo '<tr><th>文件名</th><th>大小</th><th>修改時間</th></tr>';
                    
                    // 按修改時間排序
                    usort($files, function($a, $b) {
                        return filemtime($b) - filemtime($a);
                    });
                    
                    // 只顯示前5個文件
                    for ($i = 0; $i < min(5, count($files)); $i++) {
                        $file = $files[$i];
                        $fileName = basename($file);
                        $fileSize = filesize($file);
                        $modTime = date('Y-m-d H:i:s', filemtime($file));
                        
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($fileName) . '</td>';
                        echo '<td>' . number_format($fileSize) . ' 字節</td>';
                        echo '<td>' . $modTime . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                }
            } else {
                echo '<div class="error-box">編譯目錄不存在</div>';
            }
            ?>
        </div>
        
        <div class="status-box">
            <h3>測試模板編譯</h3>
            <?php
            try {
                // 嘗試編譯模板
                $smarty->clearCompiledTemplate('GameManage.tpl');
                $smarty->assign('test_var', 'test_value');
                
                // 不實際顯示，只測試編譯
                ob_start();
                $smarty->fetch('GameManage.tpl');
                ob_end_clean();
                
                echo '<div class="success-box">模板編譯成功</div>';
            } catch (Exception $e) {
                echo '<div class="error-box">模板編譯失敗: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>
        
        <div class="status-box">
            <h3>操作建議</h3>
            <p>如果地圖選擇問題仍然存在，請嘗試以下操作：</p>
            <ol>
                <li><strong>清除瀏覽器緩存</strong> - 按 Ctrl+F5 或 Cmd+Shift+R 強制刷新</li>
                <li><strong>清除模板緩存</strong> - 刪除 templates_c/ 目錄中的所有文件</li>
                <li><strong>檢查瀏覽器控制台</strong> - 查看是否有 JavaScript 錯誤</li>
                <li><strong>檢查網絡請求</strong> - 確認 JavaScript 和 CSS 文件正確加載</li>
            </ol>
        </div>
    </div>
</body>
</html>
